<div data-object-id="<?php echo esc_attr($house_details['estateId']); ?>" class="vm-single-offerts <?php if (isset($house_details['assignment']['status']['id']) && $house_details['assignment']['status']['id'] == 10) : ?>sold<?php endif; ?>">
    <div class="vm-grid vm-grid--full single-offerts-hero">
        <div class="vm-col-12 posrel">
            <div class="gallery-buttons">
                <?php if ($gallery_data && is_array($gallery_data) && !empty($gallery_data)) : ?>
                    <button id="show-more-btn" data-expanded="false" class="vm-button vm-button--white button-slider"><?php echo __('Alla bilder', TEXTDOMAIN); ?></button>
                <?php endif; ?>
                <?php if ($plans_data && is_array($plans_data) && !empty($plans_data)) : ?>
                    <button id="show-plans-btn" data-expanded="false" class="vm-button vm-button--white button-slider"><?php echo __('Planritning', TEXTDOMAIN); ?></button>
                <?php endif; ?>
            </div>

            <div class="vm-slider js-house-slider">
                <div class="swiper-wrapper">
                    <?php
                    if ($slider_images) :
                        foreach ($slider_images as $key => $image) :
                            $extra_class = '';
                            if ($image['id'] == $post_thumbnail_id && $thumbnail_is_plan == true) {
                                $extra_class = 'plan';
                            }
                    ?>
                            <div class="swiper-slide">
                                <img class="vm-slider__item-image <?php echo esc_attr($image['orientation']); ?> <?php echo esc_attr($extra_class); ?>" src="<?php echo $image['url']; ?>" loading="lazy">
                                <div class="swiper-lazy-preloader"></div>
                            </div>
                    <?php
                            if ($key == 4) {
                                break;
                            }
                        endforeach;
                    endif;
                    ?>
                </div>
                <?php if ($slider_images && is_countable($slider_images) && count($slider_images) > 1) : ?>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-prev">
                        <svg width="14" height="24" viewBox="0 0 14 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0.93934 10.9393C0.353553 11.5251 0.353553 12.4749 0.93934 13.0607L10.4853 22.6066C11.0711 23.1924 12.0208 23.1924 12.6066 22.6066C13.1924 22.0208 13.1924 21.0711 12.6066 20.4853L4.12132 12L12.6066 3.51472C13.1924 2.92893 13.1924 1.97919 12.6066 1.3934C12.0208 0.807612 11.0711 0.807612 10.4853 1.3934L0.93934 10.9393ZM4 10.5L2 10.5L2 13.5L4 13.5L4 10.5Z" fill="black" />
                        </svg>
                    </div>
                    <div class="swiper-button-next">
                        <svg width="14" height="24" viewBox="0 0 14 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13.0607 13.0607C13.6464 12.4749 13.6464 11.5251 13.0607 10.9393L3.51472 1.3934C2.92893 0.807611 1.97919 0.807611 1.3934 1.3934C0.807611 1.97919 0.807611 2.92893 1.3934 3.51472L9.87868 12L1.3934 20.4853C0.807611 21.0711 0.807611 22.0208 1.3934 22.6066C1.97919 23.1924 2.92893 23.1924 3.51472 22.6066L13.0607 13.0607ZM11 13.5H12V10.5H11V13.5Z" fill="black" />
                        </svg>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="vm-single-offerts__content">
        <div class="vm-grid">
            <div class="vm-col-12 vm-col-xl-7 vm-col-xxl-8">
                <div class="vm-single-offerts__title flex align-items">
                    <h2>
                        <?php echo get_the_title(); ?>
                    </h2>
                    <?php if (isset($house_details['assignment']['status']['id']) && $house_details['assignment']['status']['id'] == 10) : ?>
                        <span class="vm-badge">
                            <span><?php echo __('SÅLD', TEXTDOMAIN); ?></span>
                        </span>
                    <?php elseif (isset($house_details['assignment']['status']['id']) && $house_details['assignment']['status']['id'] == 2) : ?>
                        <span class="vm-badge vm-badge--alt">
                            <span><?php echo __('KOMMANDE', TEXTDOMAIN); ?></span>
                        </span>
                    <?php endif; ?>
                    <?php if (
                        isset($house_details['internetSettings']['bidSetting'])
                        && $house_details['internetSettings']['bidSetting'] !== 'DontShowBidding' // don't show if bids visibility is set to "DontShowBidding"
                        && isset($house_details['assignment']['status']['id'])
                        && $house_details['assignment']['status']['id'] != 10 // don't show bids if house is sold
                        && isset($house_details['bids'])
                        && !empty($house_details['bids'])
                    ) { ?>
                        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class="">
                            <g>
                                <path d="M177.299 130.588c13.458 13.577 35.389 13.633 48.917.125l76.573-76.462c13.54-13.52 13.498-35.473-.093-48.942l-2.034-2.016c-4.443-4.403-11.609-4.39-16.036.03l-109.334 109.15c-4.432 4.424-4.45 11.598-.042 16.046zM254.743 212.751 76.229 391.015c.258.225.519.444.764.689l43.473 43.4c.25.249.474.514.703.776l178.514-178.265zM326.24 73.17l-84.459 84.318 112.533 112.346 84.461-84.319zM381.395 334.409l1.952 1.916c4.453 4.369 11.596 4.334 16.005-.079l109.321-109.408c4.45-4.454 4.433-11.676-.04-16.108l-2.072-2.053c-13.493-13.371-35.256-13.329-48.698.094l-76.685 76.577c-13.587 13.567-13.49 35.613.217 49.061zM55.829 412.897c-.255-.255-.485-.525-.718-.793l-45.804 45.74c-12.41 12.389-12.41 32.476 0 44.865 12.41 12.389 32.53 12.389 44.94 0l45.801-45.737c-.252-.22-.507-.434-.747-.674zM497 482h-23.168l-5.301-38.167a16.569 16.569 0 0 0-16.411-14.289H280.336a16.569 16.569 0 0 0-16.375 14.041L258.033 482h-23.44c-8.284 0-15 6.716-15 15s6.716 15 15 15H497c8.284 0 15-6.716 15-15s-6.715-15-15-15z" fill="#000000" opacity="1" data-original="#000000" class=""></path>
                            </g>
                        </svg>
                    <?php } ?>
                </div>
                <div class="vm-single-offerts__short-desc">
                    <div class="content js-details">
                        <?php
                        if (isset($house_details['description']['shortSellingDescription']) && !empty($house_details['description']['shortSellingDescription'])) {
                            $description = nl2br($house_details['description']['shortSellingDescription']);
                        } elseif (isset($house_details['description']['longSellingDescription']) && !empty($house_details['description']['longSellingDescription'])) {
                            $description = nl2br($house_details['description']['longSellingDescription']);
                        }

                        if (isset($description) && !empty($description)) {
                            $description_limit = 50;
                            $description_trimmed = custom_wp_trim_words($description, $description_limit, '');
                            $description_rest = mb_substr($description, mb_strlen($description_trimmed));
                            if (mb_substr($description_rest, 0, 1) == '.') {
                                $description_rest = mb_substr($description_rest, 1);
                            }
                            // check the length of both description parts
                            if (mb_strlen($description_trimmed) < mb_strlen($description)) {
                        ?>
                                <span class="short-description"><?php echo $description_trimmed; ?><span class="ellipsis">&hellip;</span></span><?php if (isset($house_details['assignment']['status']['id']) && $house_details['assignment']['status']['id'] != 10) : ?><span class="hidden-description" style="display: none;"><?php echo $description_rest; ?></span><?php endif; ?>
                        <?php
                            } else {
                                echo $description;
                            }
                        }
                        ?>
                    </div>
                    <?php if (isset($description) && !empty($description) && isset($house_details['assignment']['status']['id']) && $house_details['assignment']['status']['id'] != 10 && mb_strlen($description_trimmed) < mb_strlen($description)) : ?>
                        <span class="vm-button vm-button--dark js-show-details" data-readmore="Läs mer" data-readless="Läs mindre">
                            <?php echo __('Läs mer', TEXTDOMAIN); ?>
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="vm-col-12 vm-col-xl-5 vm-col-xxl-4">
                <div class="vm-single-offerts__desc">
                    <div class="vm-grid">
                        <div class="vm-col-6">
                            <?php if ($status == 10) : ?>
                                <?php if (isset($house_details['price']['finalPrice']) && !empty($house_details['price']['finalPrice'])) : ?>
                                    <h5><?php echo __('Slutpris', TEXTDOMAIN); ?></h5>
                                <?php endif; ?>
                            <?php else : ?>
                                <?php if (isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])) : ?>
                                    <?php if (isset($house_details['price']['text']) && $house_details['price']['text'] == 'accepterat pris') : ?>
                                        <h5><?php echo __('Accepterat pris', TEXTDOMAIN); ?></h5>
                                    <?php else : ?>
                                        <h5><?php echo __('Utgångspris', TEXTDOMAIN); ?></h5>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <div class="vm-col-6">
                            <?php if ($status == 10) : ?>
                                <?php if (isset($house_details['price']['finalPrice']) && !empty($house_details['price']['finalPrice'])) : ?>
                                    <h5><?php echo vm_number($house_details['price']['finalPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></h5>
                                <?php endif; ?>
                            <?php else : ?>
                                <?php if (isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])) : ?>
                                    <h5><?php echo vm_number($house_details['price']['startingPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></h5>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="vm-flex">
                        <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                            <small><?php echo __('Upplåtelseform', TEXTDOMAIN); ?></small>
                            <small><?php echo nl2br($house_details['baseInformation']['tenure']); ?></small>
                        <?php endif; ?>
                        <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                            <small><?php echo __('Ägandeform', TEXTDOMAIN); ?></small>
                            <small><?php echo nl2br($house_details['baseInformation']['tenure']); ?></small>
                        <?php endif; ?>
                        <?php if (isset($house_details['houseInterior']['numberOfRooms']) && !empty($house_details['houseInterior']['numberOfRooms'])) : ?>
                            <small><?php echo __('Rum', TEXTDOMAIN); ?></small>
                            <small><?php echo nl2br($house_details['houseInterior']['numberOfRooms']); ?></small>
                        <?php endif; ?>
                        <?php if (isset($house_details['houseInterior']['numberOffBedroom']) && !empty($house_details['houseInterior']['numberOffBedroom'])) : ?>
                            <small><?php echo __('Varav sovrum', TEXTDOMAIN); ?></small>
                            <small><?php 
                            $bedroom_text = $house_details['houseInterior']['numberOffBedroom'];
                            if (isset($house_details['houseInterior']['maxNumberOffBedroom']) && !empty($house_details['houseInterior']['maxNumberOffBedroom'])) :
                                $bedroom_text .= ' - ' . $house_details['houseInterior']['maxNumberOffBedroom'];
                            endif;
                            echo nl2br($bedroom_text);
                            ?></small>
                        <?php endif; ?>
                        <?php if (isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])) : ?>
                            <small><?php echo __('Boarea', TEXTDOMAIN); ?></small>
                            <small><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</small>
                        <?php endif; ?>
                        <?php if (isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])) : ?>
                            <small><?php echo __('Biarea', TEXTDOMAIN); ?></small>
                            <small><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</small>
                        <?php endif; ?>
                        <?php if (isset($house_details['plot']['area']) && !empty($house_details['plot']['area'])) : ?>
                            <small><?php echo __('Tomtarea', TEXTDOMAIN); ?></small>
                            <small><?php echo nl2br($house_details['plot']['area']); ?> kvm</small>
                        <?php endif; ?>
                        <?php if (isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])) : ?>
                            <small><?php echo __('Byggnadsår', TEXTDOMAIN); ?></small>
                            <small><?php echo nl2br($house_details['building']['buildingYear']) ?></small>
                        <?php endif; ?>
                        <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                            <small><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></small>
                            <small><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="vm-single-offerts__table" id="offerts-table">
        <div class="vm-grid">
            <div class="vm-col-12">
                <div class="vm-tabs">
                    <button class="tablink vm-button vm-button--dark active js-tablink" data-tabid="tab1"><?php echo __('Fakta', TEXTDOMAIN); ?></button>
                    <?php if (isset($house_details['houseInterior']['generealDescription']) && !empty($house_details['houseInterior']['generealDescription'])) : ?>
                        <button class="tablink vm-button vm-button--dark js-tablink" data-tabid="tab2"><?php echo __('Rumsbeskrivning', TEXTDOMAIN); ?></button>
                    <?php endif; ?>
                    <button class="tablink vm-button vm-button--dark js-tablink" data-tabid="tab3"><?php echo __('Byggnad', TEXTDOMAIN); ?></button>
                    <button class="tablink vm-button vm-button--dark js-tablink" data-tabid="tab5"><?php echo __('Ekonomi', TEXTDOMAIN); ?></button>
                    <?php if (get_field('documents') && is_array(get_field('documents'))) : ?>
                        <button class="tablink vm-button vm-button--dark js-tablink" data-tabid="tab6"><?php echo __('Dokument', TEXTDOMAIN); ?></button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="vm-grid">
            <div class="vm-col-12 vm-col-xl-8 vm-col-space-between-v vm-single-offerts__table__tabs" id="tabs">
                <div class="vm-tabs">

                    <button class=" tablink-mobile active js-tablink" data-tabid="tab1"><?php echo __('Fakta', TEXTDOMAIN); ?></button>
                    <div id="tab1" class="tabcontent active">
                        <table>
                            <?php if ($status == 10) : ?>
                                <?php if (isset($house_details['price']['finalPrice']) && !empty($house_details['price']['finalPrice'])) : ?>
                                    <tr>
                                        <td><strong><?php echo __('Slutpris', TEXTDOMAIN); ?></strong></td>
                                        <td><?php echo vm_number($house_details['price']['finalPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></td>
                                    </tr>
                                <?php endif; ?>
                            <?php else : ?>
                                <?php if (isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])) : ?>
                                    <tr>
                                        <?php if (isset($house_details['price']['text']) && $house_details['price']['text'] == 'accepterat pris') : ?>
                                            <td><strong><?php echo __('Accepterat pris', TEXTDOMAIN); ?></strong></td>
                                        <?php else : ?>
                                            <td><strong><?php echo __('Utgångspris', TEXTDOMAIN); ?></strong></td>
                                        <?php endif; ?>
                                        <td><?php echo vm_number($house_details['price']['startingPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></td>
                                    </tr>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Upplåtelseform', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['tenure']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['objectAddress']['streetAddress']) && !empty($house_details['baseInformation']['objectAddress']['streetAddress'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Adress', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['objectAddress']['streetAddress']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if ((isset($house_details['baseInformation']['objectAddress']['zipCode']) || isset($house_details['baseInformation']['objectAddress']['city'])) && (!empty($house_details['baseInformation']['objectAddress']['zipCode']) || !empty($house_details['baseInformation']['objectAddress']['city']))) : ?>
                                <tr>
                                    <td><strong><?php echo __('Postadress', TEXTDOMAIN); ?></strong></td>
                                    <?php
                                    $postalcode = nl2br($house_details['baseInformation']['objectAddress']['zipCode']);
                                    if (substr($postalcode, -4, 1) != ' ') {
                                        $postalcode = substr($postalcode, 0, strlen($postalcode) - 2) . ' ' . substr($postalcode, -2);
                                    }
                                    ?>
                                    <td><?php echo $postalcode . ' ' . nl2br($house_details['baseInformation']['objectAddress']['city']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['objectAddress']['area']) && !empty($house_details['baseInformation']['objectAddress']['area'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Område', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['objectAddress']['area']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['objectAddress']['municipality']) && !empty($house_details['baseInformation']['objectAddress']['municipality'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Kommun', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['objectAddress']['municipality']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['tenure']) && !empty($house_details['baseInformation']['tenure'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Ägandeform', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['tenure']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['houseInterior']['numberOfRooms']) && !empty($house_details['houseInterior']['numberOfRooms'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Rum', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['houseInterior']['numberOfRooms']); ?></td>
                                </tr>
                            <?php endif; ?>
                            
                            <?php if (isset($house_details['houseInterior']['numberOffBedroom']) && !empty($house_details['houseInterior']['numberOffBedroom'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Varav sovrum', TEXTDOMAIN); ?></strong></td>
                                    <td><?php 
                                    $bedroom_text = $house_details['houseInterior']['numberOffBedroom'];
                                    if (isset($house_details['houseInterior']['maxNumberOffBedroom']) && !empty($house_details['houseInterior']['maxNumberOffBedroom'])) :
                                        $bedroom_text .= ' - ' . $house_details['houseInterior']['maxNumberOffBedroom'];
                                    endif;
                                    echo nl2br($bedroom_text);
                                    ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['livingSpace']) && !empty($house_details['baseInformation']['livingSpace'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Boarea', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['livingSpace']); ?> kvm</td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['otherSpace']) && !empty($house_details['baseInformation']['otherSpace'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Biarea', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['otherSpace']); ?> kvm</td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['plot']['area']) && !empty($house_details['plot']['area'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Tomtarea', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['plot']['area']); ?> kvm</td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['buildingType']) && !empty($house_details['building']['buildingType'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Byggnadstyp', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['buildingType']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Byggnadsår', TEXTDOMAIN); ?></strong></td>
                                    <td>
                                        <?php echo nl2br($house_details['building']['buildingYear']); ?>
                                        <?php if (isset($house_details['building']['commentaryForBuildingYear']) && !empty($house_details['building']['commentaryForBuildingYear'])) : ?>
                                            <br>
                                            <?php echo nl2br($house_details['building']['commentaryForBuildingYear']); ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['inspection']['inspected']) && !empty($house_details['inspection']['inspected'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Förbesiktigad', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo ($house_details['inspection']['inspected'] == true) ? 'Ja' : 'Nej' ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['tvAndBroadband']['broadband']) && !empty($house_details['tvAndBroadband']['broadband'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('TV och Bredband', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['tvAndBroadband']['broadband']); ?></td>
                                </tr>
                            <?php endif; ?>

                        </table>

                    </div>

                    <?php if (isset($house_details['houseInterior']['generealDescription']) && !empty($house_details['houseInterior']['generealDescription'])) : ?>
                        <button class="tablink-mobile js-tablink" data-tabid="tab2"><?php echo __('Rumsbeskrivning', TEXTDOMAIN); ?></button>
                        <div id="tab2" class="tabcontent">
                            <div class="content-text">
                                <?php
                                $description = nl2br($house_details['houseInterior']['generealDescription']);
                                echo $description;
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <button class="tablink-mobile js-tablink" data-tabid="tab3"><?php echo __('Byggnad', TEXTDOMAIN); ?></button>
                    <div id="tab3" class="tabcontent">
                        <h3><?php echo __('Byggnadsinformation', TEXTDOMAIN); ?></h3>
                        <table>
                            <?php if (isset($house_details['building']['buildingType']) && !empty($house_details['building']['buildingType'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Byggnadstyp', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['buildingType']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['buildingYear']) && !empty($house_details['building']['buildingYear'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Byggnadsår', TEXTDOMAIN); ?></strong></td>
                                    <td>
                                        <?php echo nl2br($house_details['building']['buildingYear']); ?>
                                        <?php if (isset($house_details['building']['commentaryForBuildingYear']) && !empty($house_details['building']['commentaryForBuildingYear'])) : ?>
                                            <br>
                                            <?php echo nl2br($house_details['building']['commentaryForBuildingYear']); ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['baseInformation']['propertyUnitDesignation']) && !empty($house_details['baseInformation']['propertyUnitDesignation'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Fastighetsbeteckning', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['baseInformation']['propertyUnitDesignation']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['heating']) && !empty($house_details['building']['heating'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Uppvärmning', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['heating']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['ventilation']['type']) && !empty($house_details['ventilation']['type'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Ventilation', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['ventilation']['type']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['facade']) && !empty($house_details['building']['facade'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Fasad', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['facade']); ?></td>
                                </tr>
                            <?php endif; ?>
                            
                            <?php if (isset($house_details['building']['frame']) && !empty($house_details['building']['frame'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Stomme', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['frame']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['windows']) && !empty($house_details['building']['windows'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Fönster', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['windows']); ?></td>
                                </tr>
                            <?php endif; ?>
                            
                            <?php if (isset($house_details['building']['beam']) && !empty($house_details['building']['beam'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Bjälklag', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['beam']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['externallySheetMetalWork']) && !empty($house_details['building']['externallySheetMetalWork'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Utvändigt plåtarbete', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['externallySheetMetalWork']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['foundationWall']) && !empty($house_details['building']['foundationWall'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Grundmur', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['foundationWall']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['roof']) && !empty($house_details['building']['roof'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Tak', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['roof']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['foundation']) && !empty($house_details['building']['foundation'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Grundläggning', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['foundation']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['waterAndDrain']['info']) && !empty($house_details['waterAndDrain']['info'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Vatten och avlopp', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['waterAndDrain']['info']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['renovations']) && !empty($house_details['building']['renovations'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Renoveringar', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['renovations']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['otherAboutTheBuildning']) && !empty($house_details['building']['otherAboutTheBuildning'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Övrigt', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['otherAboutTheBuildning']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['building']['otherBuildings']) && !empty($house_details['building']['otherBuildings'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Övriga byggnader', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['building']['otherBuildings']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['enrollments']['preferentialAndCommunity']) && !empty($house_details['enrollments']['preferentialAndCommunity'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Servitut, Ga, Samfälligheter, Etc', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['enrollments']['preferentialAndCommunity']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['enrollments']['planRegulations']) && !empty($house_details['enrollments']['planRegulations'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Planbestämmelser', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['enrollments']['planRegulations']); ?></td>
                                </tr>
                            <?php endif; ?>

                        </table>

                        <h3><?php echo __('Energideklaration', TEXTDOMAIN); ?></h3>
                        <table>
                            <?php
                            if ((isset($house_details['energyDeclaration']['completed']) && !empty($house_details['energyDeclaration']['completed'])) || (isset($house_details['energyDeclaration']['energyDeclarationDate']) && !empty($house_details['energyDeclaration']['energyDeclarationDate']))) :
                                $energy_declaration = $house_details['energyDeclaration']['completed'];
                                $energy_declaration_translated = str_replace('NotPerformed', __('Inte utfört', TEXTDOMAIN), $energy_declaration);
                                $energy_declaration_translated = str_replace('Performed', __('Utfört', TEXTDOMAIN), $energy_declaration_translated);
                                $energy_declaration_translated = str_replace('NotSelected', __('Inte utfört', TEXTDOMAIN), $energy_declaration_translated);
                                $energy_declaration_translated = str_replace('Selected', __('Utfört', TEXTDOMAIN), $energy_declaration_translated);
                                $text = '';
                                if (isset($house_details['energyDeclaration']['completed']) && !empty($house_details['energyDeclaration']['completed'])) {
                                    $text .= nl2br($energy_declaration_translated);
                                }
                                if (isset($house_details['energyDeclaration']['energyDeclarationDate']) && !empty($house_details['energyDeclaration']['energyDeclarationDate'])) {
                                    $text .= ' (' . date('Y-m-d', strtotime($house_details['energyDeclaration']['energyDeclarationDate'])) . ')';
                                }
                            ?>
                                <tr>
                                    <td><strong><?php echo __('Status', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo $text; ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['energyDeclaration']['energyConsumption']) && !empty($house_details['energyDeclaration']['energyConsumption'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Primärenergital', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['energyDeclaration']['energyConsumption']); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['energyDeclaration']['energyClass']) && !empty($house_details['energyDeclaration']['energyClass']) && $house_details['energyDeclaration']['energyClass'] !== 'None') : ?>
                                <tr>
                                    <td><strong><?php echo __('Energiklass', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo nl2br($house_details['energyDeclaration']['energyClass']); ?></td>
                                </tr>
                            <?php endif; ?>

                        </table>
                    </div>
                    
                    <button class=" tablink-mobile js-tablink" data-tabid="tab5"><?php echo __('Ekonomi', TEXTDOMAIN); ?></button>
                    <div id="tab5" class="tabcontent">
                        <h3><?php echo __('Ekonomi', TEXTDOMAIN); ?></h3>
                        <table>
                            <?php if ($status == 10) : ?>
                                <?php if (isset($house_details['price']['finalPrice']) && !empty($house_details['price']['finalPrice'])) : ?>
                                    <tr>
                                        <td><strong><?php echo __('Pris', TEXTDOMAIN); ?></strong></td>
                                        <td><?php echo vm_number($house_details['price']['finalPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></td>
                                    </tr>
                                <?php endif; ?>
                            <?php else : ?>
                                <?php if (isset($house_details['price']['startingPrice']) && !empty($house_details['price']['startingPrice'])) : ?>
                                    <tr>
                                        <td><strong><?php echo __('Pris', TEXTDOMAIN); ?></strong></td>
                                        <td><?php echo vm_number($house_details['price']['startingPrice']) . ' ' . __('kr', TEXTDOMAIN); ?></td>
                                    </tr>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if (isset($house_details['participationAndRepairFund']['indirectNetDebt']) && !empty($house_details['participationAndRepairFund']['indirectNetDebt'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Indirekt nettoskuldsättning', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['participationAndRepairFund']['indirectNetDebt']) . ' ' . __('kr', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                        </table>

                        <?php if(
                            isset($house_details['operation']['heating']) && !empty($house_details['operation']['heating']) ||
                            isset($house_details['operation']['electricity']) && !empty($house_details['operation']['electricity']) ||
                            isset($house_details['operation']['waterAndDrain']) && !empty($house_details['operation']['waterAndDrain']) ||
                            isset($house_details['operation']['chimneySweeping']) && !empty($house_details['operation']['chimneySweeping']) ||
                            isset($house_details['operation']['roadCommunity']) && !empty($house_details['operation']['roadCommunity']) ||
                            isset($house_details['operation']['sanitation']) && !empty($house_details['operation']['sanitation']) ||
                            isset($house_details['operation']['insurance']) && !empty($house_details['operation']['insurance']) ||
                            isset($house_details['operation']['other']) && !empty($house_details['operation']['other']) ||
                            isset($house_details['operation']['sum']) && !empty($house_details['operation']['sum']) ||
                            isset($house_details['operation']['commentary']) && !empty($house_details['operation']['commentary']) ||
                            isset($house_details['operation']['personsInTheHousehold']) && !empty($house_details['operation']['personsInTheHousehold'])
                        ) { ?>
                        <h3><?php echo __('Driftkostnader', TEXTDOMAIN); ?></h3>
                        <table>
                            <?php if (isset($house_details['operation']['heating']) && !empty($house_details['operation']['heating'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Uppvärmning', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['heating']) . ' ' . __('kr/år', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['operation']['electricity']) && !empty($house_details['operation']['electricity'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('El', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['electricity']) . ' ' . __('kr/år', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['operation']['waterAndDrain']) && !empty($house_details['operation']['waterAndDrain'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('VA', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['waterAndDrain']) . ' ' . __('kr/år', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['operation']['chimneySweeping']) && !empty($house_details['operation']['chimneySweeping'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Sotning', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['chimneySweeping']) . ' ' . __('kr/år', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['operation']['roadCommunity']) && !empty($house_details['operation']['roadCommunity'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Väg/samfällighetsavgift', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['roadCommunity']) . ' ' . __('kr/år', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['operation']['sanitation']) && !empty($house_details['operation']['sanitation'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Renhållning', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['sanitation']) . ' ' . __('kr/år', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['operation']['insurance']) && !empty($house_details['operation']['insurance'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Försäkring', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['insurance']) . ' ' . __('kr/år', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if (isset($house_details['operation']['other']) && !empty($house_details['operation']['other'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Övrigt', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['other']) . ' ' . __('kr/år', TEXTDOMAIN); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php
                            $text = '';
                            if (isset($house_details['operation']['sum']) && !empty($house_details['operation']['sum'])) {
                                $text .= vm_number($house_details['operation']['sum']) . ' ' . __('kr/år', TEXTDOMAIN);
                            }
                            if (isset($house_details['operation']['commentary']) && !empty($house_details['operation']['commentary'])) {
                                $text .= '<br>' . nl2br($house_details['operation']['commentary']);
                            }
                            if (!empty($text)) {
                            ?>
                                <tr>
                                    <td><strong><?php echo __('Summa', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo $text; ?></td>
                                </tr>
                            <?php } ?>

                            <?php if (isset($house_details['operation']['personsInTheHousehold']) && !empty($house_details['operation']['personsInTheHousehold'])) : ?>
                                <tr>
                                    <td><strong><?php echo __('Personer i hushållet', TEXTDOMAIN); ?></strong></td>
                                    <td><?php echo vm_number($house_details['operation']['personsInTheHousehold']); ?></td>
                                </tr>
                            <?php endif; ?>

                        </table>
                        <?php } ?>
                    </div>

                    <?php if (get_field('documents') && is_array(get_field('documents'))) : ?>
                        <button class=" tablink-mobile js-tablink" data-tabid="tab6"><?php echo __('Dokument', TEXTDOMAIN); ?></button>
                        <div id="tab6" class="tabcontent">
                            <h3><?php echo __('Bostadsinformation', TEXTDOMAIN); ?></h3>
                            <?php
                            $docs_list = get_field('documents');
                            if ($docs_list && is_array($docs_list)) {

                                // Compare with the association documents
                                $post_date_change = get_field('date_change');
                                if ($post_date_change !== '01/01/1900 12:00 am') {
                                    $association_docs_different = false;
                                    $docs_count = (is_countable($docs_list)) ? count($docs_list) : 0;
                                    $association_docs = $house_details['association']['documents'];
                                    $association_docs_count = (is_countable($association_docs)) ? count($association_docs) : 0;
                                    if ($docs_count != $association_docs_count) {
                                        $association_docs_different = true;
                                    } else {
                                        foreach ($docs_list as $item) {
                                            $api_id = $item['api_id'];
                                            $date_change = $item['date_change'];
                                            $found = false;
                                            foreach ($association_docs as $association_doc) {
                                                if ($api_id == $association_doc['id'] && $date_change == $association_doc['dateChangedData']) {
                                                    $found = true;
                                                    break;
                                                }
                                            }
                                            if (!$found) {
                                                $association_docs_different = true;
                                                break;
                                            }
                                        }
                                    }

                                    // If there is a difference in association documents, set date change to 01/01/1900 to force download of new documents
                                    if ($association_docs_different) {
                                        vitec_log('FOUND CHANGES IN THE ASSOCIATION DOCUMENTS ON POST: ' . get_the_ID());
                                        update_post_meta(get_the_ID(), 'date_change', '01/01/1900 12:00 am');
                                    }
                                }
                                ?>

                                <table>
                                    <?php foreach ($docs_list as $item) { ?>
                                        <tr>
                                            <td><a href="<?php echo wp_get_attachment_url($item['file']) ?>"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/svg/download-btn.svg"> <?php echo $item['name'] ?> </a></td>
                                        </tr>
                                    <?php } ?>
                                </table>
                            <?php } ?>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
            <div class="vm-col-12 vm-col-xl-4 vm-single-offerts__table__viewings">
                <?php
                $timeslots_i = 0;
                $timeslots_aggregated = [];

                if (isset($house_details['viewings']) && !empty($house_details['viewings'])) {

                    // Get viewing details
                    $viewings = isset($GLOBALS['viewings']) ? $GLOBALS['viewings'] : [];
                    if (!empty($viewings)) {
                        ?>
                        <div class="vm-single-offerts__visits">
                            <div class="vm-single-offerts__visits__rows">
                                <?php

                                // Aggregate timeslots
                                foreach ($viewings as $viewing) {

                                    if(!isset($viewing['timeSlots']))
                                        continue;

                                    // Skip if timeslot is not supposed to be shown on internet
                                    if($viewing['showOnInternet'] == false)
                                        continue;

                                    // Itarate through timeslots
                                    $timeslots = $viewing['timeSlots'];
                                    if (empty($timeslots)) {
                                        continue;
                                    }

                                    foreach ($timeslots as $timeslot) {

                                        // Add to aggregated array
                                        $timeslots_aggregated[] = [
                                            'id'         => $timeslot['id'],
                                            'startsAt'   => $timeslot['startsAt'],
                                            'endsAt'     => $timeslot['endsAt'],
                                            'commentary' => $viewing['commentary'],
                                        ];
                                    }
                                }

                                // Sort aggregated timeslots by date
                                if ($timeslots_aggregated) {
                                    usort($timeslots_aggregated, function ($a, $b) {
                                        return strtotime($a['startsAt']) <=> strtotime($b['startsAt']);
                                    });
                                }

                                // Show only 4 first timeslots
                                foreach ($timeslots_aggregated as $timeslot) {

                                    // Get date and time
                                    $start_timestamp = strtotime($timeslot['startsAt']);
                                    $end_timestamp = strtotime($timeslot['endsAt']);

                                    // Compare starting date with current date, and only show future dates
                                    $now = current_time('timestamp');
                                    if ($start_timestamp < $now) {
                                        continue;
                                    }

                                    // Format date and time
                                    setlocale(LC_TIME, 'sv_SE');
                                    if ($end_timestamp - $start_timestamp == 86400) {
                                        $time = date_i18n('l - j F', $start_timestamp);
                                    } else {
                                        $formatted_start_time = date_i18n('l - j F - \k\l G:i', $start_timestamp);
                                        $formatted_end_time = date_i18n('G:i', $end_timestamp);
                                        $time = $formatted_start_time . ' - ' . $formatted_end_time;
                                    }
                                    $timeslots_i++;
                                    ?>
                                    <a class="vm-single-offerts__visits__row <?php if ($timeslots_i > 4) { echo 'hidden'; } ?>" href="#contact" data-timeslot-id="<?php echo $timeslot['id']; ?>">
                                        <div class="icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" height="16" width="14" viewBox="0 0 448 512">
                                                <path d="M400 64h-48V12c0-6.6-5.4-12-12-12h-8c-6.6 0-12 5.4-12 12v52H128V12c0-6.6-5.4-12-12-12h-8c-6.6 0-12 5.4-12 12v52H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zM48 96h352c8.8 0 16 7.2 16 16v48H32v-48c0-8.8 7.2-16 16-16zm352 384H48c-8.8 0-16-7.2-16-16V192h384v272c0 8.8-7.2 16-16 16z" />
                                            </svg>
                                        </div>
                                        <div class="inner">
                                            <div class="date">
                                                <?php echo esc_html($time); ?>
                                            </div>
                                            <div class="desc"><?php echo esc_html($timeslot['commentary']); ?></div>
                                        </div>
                                    </a>
                                <?php
                                }
                                ?>
                            </div>
                            <?php if ($timeslots_i > 0) { ?>
                                <h3><?php echo __('Visningar', TEXTDOMAIN); ?></h3>
                            <?php } ?>
                            <?php if ($timeslots_i > 4) { ?>
                                <div class="links">
                                    <a class="vm-single-offerts__visits__more" href="#"><?php echo __('Fler datum', TEXTDOMAIN); ?></a>
                                </div>
                            <?php } ?>
                        </div>
                        <?php 
                    }
                }
                if (
                    isset($house_details['internetSettings']['bidSetting'])
                    && $house_details['internetSettings']['bidSetting'] !== 'DontShowBidding' // don't show if bids visibility is set to "DontShowBidding"
                    && isset($house_details['assignment']['status']['id'])
                    && $house_details['assignment']['status']['id'] != 10 // don't show bids if house is sold
                    && isset($house_details['bids'])
                    && !empty($house_details['bids'])
                ) {
                    // Only show highest bid if setting is set to "ShowHighestBid"
                    if ($house_details['internetSettings']['bidSetting'] == 'ShowHighestBid') {
                        usort($house_details['bids'], function ($a, $b) {
                            return $b['amount'] <=> $a['amount'];
                        });
                        $house_details['bids'] = array_slice($house_details['bids'], 0, 1);
                    }
                ?>
                    <div class="vm-single-offerts__bids">
                        <div class="vm-single-offerts__bids__rows">
                            <?php
                            $i = 0;
                            if ($house_details['internetSettings']['bidSetting'] !== 'ShowBidding') {
                                foreach ($house_details['bids'] as $bid) {

                                    // Skip cancelled bids
                                    if ($bid['cancelled'] == true) {
                                        continue;
                                    }
                                    $i++;

                                    // Get date and time
                                    $bid_date = strtotime($bid['dateAndTime']);

                                    // Format date and time
                                    setlocale(LC_TIME, 'sv_SE');
                                    $formatted_bid_date = date_i18n('j F Y - G:i', $bid_date);
                            ?>
                                    <div class="vm-single-offerts__bids__row <?php if ($i > 3) {
                                                                                    echo 'hidden';
                                                                                } ?>">
                                        <div class="icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class="">
                                                <g>
                                                    <path d="M177.299 130.588c13.458 13.577 35.389 13.633 48.917.125l76.573-76.462c13.54-13.52 13.498-35.473-.093-48.942l-2.034-2.016c-4.443-4.403-11.609-4.39-16.036.03l-109.334 109.15c-4.432 4.424-4.45 11.598-.042 16.046zM254.743 212.751 76.229 391.015c.258.225.519.444.764.689l43.473 43.4c.25.249.474.514.703.776l178.514-178.265zM326.24 73.17l-84.459 84.318 112.533 112.346 84.461-84.319zM381.395 334.409l1.952 1.916c4.453 4.369 11.596 4.334 16.005-.079l109.321-109.408c4.45-4.454 4.433-11.676-.04-16.108l-2.072-2.053c-13.493-13.371-35.256-13.329-48.698.094l-76.685 76.577c-13.587 13.567-13.49 35.613.217 49.061zM55.829 412.897c-.255-.255-.485-.525-.718-.793l-45.804 45.74c-12.41 12.389-12.41 32.476 0 44.865 12.41 12.389 32.53 12.389 44.94 0l45.801-45.737c-.252-.22-.507-.434-.747-.674zM497 482h-23.168l-5.301-38.167a16.569 16.569 0 0 0-16.411-14.289H280.336a16.569 16.569 0 0 0-16.375 14.041L258.033 482h-23.44c-8.284 0-15 6.716-15 15s6.716 15 15 15H497c8.284 0 15-6.716 15-15s-6.715-15-15-15z" fill="#000000" opacity="1" data-original="#000000" class=""></path>
                                                </g>
                                            </svg>
                                        </div>
                                        <div class="inner">
                                            <div class="date">
                                                <?php echo vm_number($bid['amount']) . ' ' . __('kr', TEXTDOMAIN); ?>
                                            </div>
                                            <div class="desc">
                                                <?php
                                                if (isset($bid['alias']) && !empty($bid['alias'])) {
                                                    echo '<div>' . $bid['alias'] . '</div>';
                                                }
                                                echo esc_html($formatted_bid_date);
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php
                                }
                            } else {
                                ?>
                                <p class="text-normal"><?php _e('Budgivning pågår', TEXTDOMAIN); ?></p>
                            <?php } ?>
                        </div>
                        <?php if ($house_details['internetSettings']['bidSetting'] == 'ShowBidding') { ?>
                            <h3><?php echo __('Budgivning pågår', TEXTDOMAIN); ?></h3>
                            <?php } elseif ($i > 0) {
                            if ($house_details['internetSettings']['bidSetting'] == 'ShowHighestBid') { ?>
                                <h3><?php echo __('Högsta bud', TEXTDOMAIN); ?></h3>
                            <?php } elseif ($house_details['internetSettings']['bidSetting'] == 'ShowBiddhistory') { ?>
                                <h3><?php echo __('Budgivning', TEXTDOMAIN); ?></h3>
                            <?php } ?>
                        <?php } ?>
                        <?php if ($i > 3) { ?>
                            <div class="links">
                                <a class="vm-single-offerts__bids__more" href="#"><?php echo __('Alla bud', TEXTDOMAIN); ?></a>
                            </div>
                        <?php } ?>
                    </div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>