<?php

/**
 * Set the maximum execution time for the script to unlimited.
 */
set_time_limit(900); // 15 minutes

/**
 * Registers Vitec API integration endpoints.
 *
 * This function registers three REST routes for Vitec API integration:
 * - /vitec/v1/update-regular: Performs a regular update.
 * - /vitec/v1/update-full: Performs a full update.
 * - /vitec/v1/delete-data: Deletes data imported by Vitec API.
 *
 * @since 1.0.0
 */
function update_vitec_endpoints() {

    // regular update
    register_rest_route(
        'vitec/v1',
        '/update-regular',
        [
            'methods'               => 'GET',
            'callback'              => 'update_vitec_regular',
            'permission_callback'     => '__return_true',
            'show_in_index'         => false
        ]
    );

    // full update
    register_rest_route(
        'vitec/v1',
        '/update-full',
        [
            'methods'               => 'GET',
            'callback'              => 'update_vitec_full',
            'permission_callback'     => '__return_true',
            'show_in_index'         => false
        ]
    );

    // replace update
    register_rest_route(
        'vitec/v1',
        '/update-replace',
        [
            'methods'               => 'GET',
            'callback'              => 'update_vitec_replace',
            'permission_callback'     => '__return_true',
            'show_in_index'         => false
        ]
    );

    // replace media
    register_rest_route(
        'vitec/v1',
        '/update-media-replace',
        [
            'methods'               => 'GET',
            'callback'              => 'update_vitec_media_replace',
            'permission_callback'     => '__return_true',
            'show_in_index'         => false
        ]
    );

    // delete data imported by Vitec API
    register_rest_route(
        'vitec/v1',
        '/delete-data',
        [
            'methods'               => 'GET',
            'callback'              => 'delete_vitec_data',
            'permission_callback'     => '__return_true',
            'show_in_index'         => false
        ]
    );
}
add_action('rest_api_init', 'update_vitec_endpoints');

/**
 * Updates Vitec data using regular update (refreshes text data, main thumbnail, viewings and bids for all houses).
 *
 * @param array $request The request data.
 * @return WP_REST_Response|WP_Error The response object or error object.
 */
function update_vitec_regular($request) {
    $response = '';
    if ($request['token'] === 'zem!yxm9dng7cvp@JRQ') {
        // run the update script
        $update = update_all_estates($update_media = false, $force = true);
        // success response
        if ($update === 1) {
            $response = new WP_REST_Response([
                'status'        => 'vitec_regular_update_success',
                'response'      => 'Vitec data successfully updated (Regular Update).'
            ]);
            $response->set_status(200);
        } 
        // already running response
        else if ($update === 2) {
            $response = new WP_Error(
                'rest_api_invalid',
                'Update is already running.',
                array('status' => 425)
            );
        }
        // error response
        else if ($update === 0) {
            $response = new WP_Error(
                'rest_api_invalid',
                'Error downloading Vitec data.',
                array('status' => 404)
            );
        }
    } else {
        // not authenticated response
        $response = new WP_Error(
            'rest_api_invalid',
            'Could not authenticate the request.',
            array('status' => 401)
        );
    }
    return $response;
}

/**
 * Updates Vitec data using full update (refreshes all the data including gallery, floor plans and PDF documents).
 *
 * @param array $request The request data.
 * @return WP_REST_Response|WP_Error The response object or error object.
 */
function update_vitec_full($request) {
    $response = '';
    if ($request['token'] === 'vqbSavyWFYD0ctf!acn') {
        // run the update script
        $update = update_all_estates($update_media = true, $force = false, $update_single_property = false, $replace_existing = false, $start_with = false, $trash_old = true);
        // success response
        if ($update === 1) {
            $response = new WP_REST_Response([
                'status'        => 'vitec_full_update_success',
                'response'      => 'Vitec data successfully updated (Full Update).'
            ]);
            $response->set_status(200);
        }
        // already running response
        else if ($update === 2) {
            $response = new WP_Error(
                'rest_api_invalid',
                'Update is already running.',
                array('status' => 425)
            );
        }
        // error response
        else if ($update === 0) {
            $response = new WP_Error(
                'rest_api_invalid',
                'Error downloading Vitec data.',
                array('status' => 404)
            );
        }
    } else {
        // not authenticated response
        $response = new WP_Error(
            'rest_api_invalid',
            'Could not authenticate the request.',
            array('status' => 401)
        );
    }
    return $response;
}

/**
 * Updates Vitec data using replace update (delete all the data, create post again, then redownload everything).
 *
 * @param array $request The request data.
 * @return WP_REST_Response|WP_Error The response object or error object.
 */
function update_vitec_replace($request) {
    $response = '';
    if ($request['token'] === 'niKFo8zPyiJ9pc4eTmXr') {
        // run the update script
        $update = update_all_estates($update_media = true, $force = false, $update_single_property = false, $replace_existing = true, $start_with = false);
        // success response
        if ($update === 1) {
            $response = new WP_REST_Response([
                'status'        => 'vitec_full_update_success',
                'response'      => 'Vitec data successfully updated (Replace).'
            ]);
            $response->set_status(200);
        }
        // already running response
        else if ($update === 2) {
            $response = new WP_Error(
                'rest_api_invalid',
                'Update is already running.',
                array('status' => 425)
            );
        }
        // error response
        else if ($update === 0) {
            $response = new WP_Error(
                'rest_api_invalid',
                'Error downloading Vitec data.',
                array('status' => 404)
            );
        }
    } else {
        // not authenticated response
        $response = new WP_Error(
            'rest_api_invalid',
            'Could not authenticate the request.',
            array('status' => 401)
        );
    }
    return $response;
}

/**
 * Updates Vitec media files (redownload all images).
 *
 * @param array $request The request data.
 * @return WP_REST_Response|WP_Error The response object or error object.
 */
function update_vitec_media_replace($request) {
    $response = '';
    if ($request['token'] === 'kmc2xgj9ezp9DGK_xdh') {
        // run the update script
        $update = update_all_estates($update_media = true, $force = true);
        // success response
        if ($update === 1) {
            $response = new WP_REST_Response([
                'status'        => 'vitec_media_replace_update_success',
                'response'      => 'Vitec data successfully updated (Media Replace)'
            ]);
            $response->set_status(200);
        }
        // already running response
        else if ($update === 2) {
            $response = new WP_Error(
                'rest_api_invalid',
                'Update is already running.',
                array('status' => 425)
            );
        }
        // error response
        else if ($update === 0) {
            $response = new WP_Error(
                'rest_api_invalid',
                'Error downloading Vitec data.',
                array('status' => 404)
            );
        }
    } else {
        // not authenticated response
        $response = new WP_Error(
            'rest_api_invalid',
            'Could not authenticate the request.',
            array('status' => 401)
        );
    }
    return $response;
}

/**
 * Delete all houses and its meta data.
 *
 * @param array $request The request data.
 * @return WP_REST_Response|WP_Error The response object or error object.
 */
function delete_vitec_data($request) {
    $response = '';
    if ($request['token'] === 'qza8kxk0xfe1NKX*vzh' && in_array($request['mode'], ['old', 'new'])) {
        // run the delete script
        if ($request['mode'] === 'old') {
            delete_estates_data($remove_old = true);
        } else {
            delete_estates_data($remove_old = false);
        }
        // success response
        $response = new WP_REST_Response([
            'status'        => 'vitec_data_delete_success',
            'response'      => 'Vitec data successfully deleted.'
        ]);
        $response->set_status(200);
    } else {
        // not authenticated response
        $response = new WP_Error(
            'rest_api_invalid',
            'Could not authenticate the request.',
            array('status' => 401)
        );
    }
    return $response;
}

/**
 * Retrieves all estates from the API.
 *
 * @return string|array Returns the response from the API or an error message.
 */
function get_all_estates() {
    $url = "https://connect.maklare.vitec.net/Estate/GetEstateList";
    $username = "670"; // Replace with your username
    $password = "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS"; // Replace with your password

    $data = json_encode([
        "customerId" => "M22127", // Replace with actual customer ID
        "typeOfDate" => 0,        // Adjust parameter as per API documentation
        "statuses"   => [
            [
                "id"    => 3,
                "name"  => "Till salu"
            ],
            [
                "id"    => 2,
                "name"  => "Intaget"
            ],
            [
                "id"    => 10,
                "name"  => "Såld/Referensobjekt"
            ]
        ]
    ]);

    $ch = curl_init();

    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4

    curl_setopt($ch, CURLOPT_USERNAME, $username);
    curl_setopt($ch, CURLOPT_PASSWORD, $password);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data)
    ));

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if ($http_code != 200) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - get_all_estates - ' . $error);
        return false;
    }

    return $result;
}


/**
 * Updates the JSON data field for a specific estate.
 *
 * @param string $estate_id   The ID of the estate.
 * @param string $estate_type The type of the estate.
 * @param int    $post_id     The ID of the post.
 * @return void
 */
function update_estate_json($estate_id, $estate_type, $post_id) {
    $estate_details = get_estate_details($estate_id, $estate_type);

    if ($estate_details) {
        update_field('json_data', wp_slash($estate_details), $post_id);
    }
}

/**
 * Updates all estates with the option to update media, force update, or update a single property.
 *
 * @param bool $update_media Whether to update the media (galleries and thumbnails) or not.
 * @param bool $force Whether to force the update even if the date changed is not newer than the saved date.
 * @param bool|int $update_single_property The ID of the specific property to update. Set to false to update all properties.
 * @return void
 */
function update_all_estates($update_media = false, $force = false, $update_single_property = false, $replace_existing = false, $start_with = false, $trash_old = false) {

    $update_running = get_option('vitec_update_running');
    $last_update = get_option('vitec_last_update');
    
    // Check for stuck updates
    if ($update_running && $last_update) {
        $last_update_time = strtotime($last_update);
        // If update has been running for more than 1 hour
        if ((time() - $last_update_time) > 3600) {
            update_option('vitec_update_running', false);
            vitec_log('WARNING: Reset stuck-update lock');
            $update_running = false;
        }
    }

    // Check if update is already running
    if ($update_running) {
        // already running response
        return 2;
    }

    // save information to options table that the update is running
    update_option('vitec_update_running', true);

    // get all houses with basic information
    $estates = get_all_estates();
    if ($estates === false) {
        // save information to options table that the update is done
        update_option('vitec_update_running', false);

        // log error
        vitec_log('ERROR: Could not retrieve estates from API');

        // error response
        return 0;
    }

    $estates = json_decode($estates, true);

    // allowed status
    $allow_status = [
        3, // Till salu
        2, // Kommande
        10, // Såld/Referensobjekt
    ];

    // get all houses posts
    $saved_estates = get_all_estates_with_ids();
    if (is_array($saved_estates)) {
        $saved_estates_by_id = array_column($saved_estates, 'ID', 'house_id');
    } else {
        $saved_estates_by_id = [];
    }

    $api_estates_ids = [];
    if (isset($estates[0])) {
        foreach ($estates[0] as $estate_type_name => $estate_type) {
            if (isset($estate_type) && is_array($estate_type) && !empty($estate_type)) {
                foreach ($estate_type as $estate) {
                    
                    // track if there is an error during the update of this property
                    $api_error = false;

                    // update specific post
                    if ($update_single_property !== false) {
                        if ($estate['id'] !== $update_single_property) {
                            continue;
                        }
                    }

                    // start with specific post
                    if ($start_with !== false) {
                        if ($estate['id'] !== $start_with) {
                            continue;
                        } else {
                            $start_with = false;
                        }
                    }

                    // only save/update if status is allowed
                    $status_id = (isset($estate['status']['id'])) ? intval($estate['status']['id']) : 0;
                    if (in_array($status_id, $allow_status)) {
                        $api_estates_ids[] = $estate['id'];

                        // find a post with the same house_id
                        $existing_post_id = null;
                        if (isset($saved_estates_by_id[$estate['id']])) {
                            $existing_post_id = $saved_estates_by_id[$estate['id']];

                            // if replace existing is true, delete the existing post and its images
                            if ($replace_existing === true) {

                                // debug
                                vitec_log('REPLACING EXISTING POST: ' . $existing_post_id);

                                $existing_images = get_posts([
                                    'post_type' => 'attachment',
                                    'numberposts' => -1,
                                    'post_status' => 'any',
                                    'post_parent' => $existing_post_id,
                                    'fields' => 'ids'
                                ]);
                                foreach ($existing_images as $existing_image_id) {
                                    wp_delete_attachment($existing_image_id, true);
                                }
                                wp_delete_post($existing_post_id, true);
                                $existing_post_id = null;
                            }
                        }

                        // if post exists update post meta
                        if ($existing_post_id) {
                            $change_in_api_logged = false;

                            // update the title
                            $post_title = get_the_title($existing_post_id);
                            if ($post_title !== $estate['streetAddress']) {

                                // debug
                                $change_in_api_logged = true;
                                vitec_log('NEW CHANGE IN API - UPDATING POST: ' . $existing_post_id);
                                vitec_log('updating-title-and-slug on post: ' . $existing_post_id);

                                wp_update_post([
                                    'ID'            => $existing_post_id,
                                    'post_title'    => wp_strip_all_tags($estate['streetAddress']),
                                    'post_name'     => sanitize_title(wp_strip_all_tags($estate['streetAddress']))
                                ]);
                            }

                            // only update if date changed is newer than saved date
                            $date_changed_saved = strtotime(get_post_meta($existing_post_id, 'date_change', true));
                            $date_changed = strtotime($estate['dateChanged']);

                            if ($force === true || $date_changed > $date_changed_saved) {
                                
                                // details in json
                                $estate_details = get_estate_details($estate['id'], $estate_type_name);
                                if ($estate_details === false) {
                                    $api_error = true;
                                }

                                if ($estate_details) {

                                    // debug
                                    if ($change_in_api_logged === false) {
                                        vitec_log('NEW CHANGE IN API - UPDATING POST: ' . $existing_post_id);
                                    }
                                    
                                    update_post_meta($existing_post_id, 'json_data', wp_slash($estate_details));
                                    update_post_meta($existing_post_id, 'house_id', $estate['id']);
                                    update_post_meta($existing_post_id, 'status', $estate['status']['name']);
                                    update_post_meta($existing_post_id, 'status_id', $status_id);
                                    update_post_meta($existing_post_id, 'livingspace', $estate['livingSpace']);
                                    update_post_meta($existing_post_id, 'rooms', $estate['rooms']);
                                    update_post_meta($existing_post_id, 'price', $estate['price']);
                                    update_post_meta($existing_post_id, 'address', $estate['streetAddress']);
                                    update_post_meta($existing_post_id, 'area', $estate['areaName']);
                                    update_post_meta($existing_post_id, 'property_type', $estate_type_name);

                                    // publish/unpublish
                                    $estate_details_array = json_decode($estate_details, true);
                                    if (in_array($status_id, [3, 10])) {
                                        // if status is Till salu or Såld/Referensobjekt, check homepage field
                                        if (isset($estate_details_array['advertiseOn']['homepage'])) {
                                            if ($estate_details_array['advertiseOn']['homepage'] == true) {
                                                wp_update_post([
                                                    'ID'            => $existing_post_id,
                                                    'post_status'   => 'publish',
                                                ]);
                                            } else {
                                                wp_update_post([
                                                    'ID'            => $existing_post_id,
                                                    'post_status'   => 'upcoming',
                                                ]);
                                            }
                                        }
                                    } elseif ($status_id == 2) {
                                        // if status is Kommande, check showAsComming field
                                        if (isset($estate_details_array['advertiseOn']['showAsComming'])) {
                                            if ($estate_details_array['advertiseOn']['showAsComming'] == true) {
                                                wp_update_post([
                                                    'ID'            => $existing_post_id,
                                                    'post_status'   => 'publish',
                                                ]);
                                            } else {
                                                wp_update_post([
                                                    'ID'            => $existing_post_id,
                                                    'post_status'   => 'upcoming',
                                                ]);
                                            }
                                        }
                                    }

                                    // Municipality
                                    update_post_meta($existing_post_id, 'municipality', $estate_details_array['baseInformation']['objectAddress']['municipality']);

                                    // set post thumbnail
                                    if (isset($estate['mainImage']['imageId']) && !empty($estate['mainImage']['imageId'])) {

                                        // thumbnail data
                                        $thumbnail_data = get_field('thumbnail_data', $existing_post_id);

                                        // thumbnail
                                        $current_thumbnail = get_post_thumbnail_id($existing_post_id);

                                        // check if thumbnail is a floor plan
                                        $is_plan = false;
                                        if (isset($estate_details_array['images']) && !empty($estate_details_array['images'])) {
                                            $image_row = array_filter($estate_details_array['images'], function ($item) use ($estate) {
                                                return $item['imageId'] == $estate['mainImage']['imageId'];
                                            });
                                            $image_category = (isset($image_row[0]['category'])) ? $image_row[0]['category'] : '';
                                            $image_text = (isset($image_row[0]['text'])) ? $image_row[0]['text'] : '';
                                            if (
                                                strpos($image_category, 'Planritning') !== false
                                                || strpos($image_category, 'planritning') !== false
                                                || strpos($image_text, 'Planritning') !== false
                                                || strpos($image_text, 'planritning') !== false
                                            ) {
                                                $is_plan = true;
                                            }
                                        }

                                        // if there is no thumbnail, set it
                                        if (empty($current_thumbnail) || !isset($thumbnail_data[0])) {
                                            $image_base = get_image_api($estate['mainImage']['imageId']);
                                            if ($image_base === false) {
                                                $api_error = true;
                                            }
                                            if ($image_base) {
                                                $image_thumbnail_id = upload_base64_file(base64_encode($image_base), $estate['mainImage']['imageId'] . '_thumb', $existing_post_id);
                                                if ($image_thumbnail_id) {
                                                    set_post_thumbnail($existing_post_id, $image_thumbnail_id);

                                                    // save thumbnail data
                                                    $thumbnail_data = [];
                                                    $thumbnail_data[] = [
                                                        'id' => $image_thumbnail_id,
                                                        'date_change' => $estate['mainImage']['dateChanged'],
                                                        'is_plan' => $is_plan,
                                                        'show' => $estate['mainImage']['showImageOnInternet']
                                                    ];

                                                    // debug
                                                    vitec_log('new-thumbnail-added: ' . $image_thumbnail_id);

                                                    update_field('thumbnail_data', $thumbnail_data, $existing_post_id);
                                                }
                                            }
                                        }

                                        // if there is a thumbnail, check if it needs to be updated
                                        else {
                                            $image_date_change = strtotime($thumbnail_data[0]['date_change']);
                                            $api_image_date_change = strtotime($estate['mainImage']['dateChanged']);

                                            if ($force === true || $api_image_date_change > $image_date_change) {
                                                $image_base = get_image_api($estate['mainImage']['imageId']);
                                                if ($image_base === false) {
                                                    $api_error = true;
                                                }
                                                if ($image_base) {

                                                    // debug
                                                    vitec_log('thumbnail-needs-update: ' . $estate['mainImage']['imageId']);
                                                    vitec_log('deleting-thumbnail: ' . $current_thumbnail);

                                                    // delete current thumbnail
                                                    wp_delete_attachment($current_thumbnail, true);
                                                    
                                                    // upload new thumbnail
                                                    $image_thumbnail_id = upload_base64_file(base64_encode($image_base), $estate['mainImage']['imageId'] . '_thumb', $existing_post_id);
                                                    if ($image_thumbnail_id) {
                                                        set_post_thumbnail($existing_post_id, $image_thumbnail_id);
                                                        // get position of current thumbnail in thumbnail data array
                                                        $current_thumbnail_position = array_search($current_thumbnail, array_column($thumbnail_data, 'id'));

                                                        // debug
                                                        vitec_log('adding-thumbnail: ' . $image_thumbnail_id);

                                                        // replace image data with new data on the same position
                                                        $thumbnail_data[$current_thumbnail_position] = [
                                                            'id' => $image_thumbnail_id,
                                                            'date_change' => $estate['mainImage']['dateChanged'],
                                                            'is_plan' => $is_plan,
                                                            'show' => $estate['mainImage']['showImageOnInternet']
                                                        ];
                                                        update_field('thumbnail_data', $thumbnail_data, $existing_post_id);
                                                    }
                                                }
                                            } else {

                                                // debug
                                                // vitec_log('skipping: thumbnail-dates-match');
                                            }
                                        }
                                    }

                                    if ($update_media) {

                                        // galleries
                                        $estate_details_array = json_decode($estate_details, true);
                                        $image_gallery = get_field('gallery_data', $existing_post_id);
                                        $plans_gallery = get_field('plans_data', $existing_post_id);
                                        $saved_gallery_images = (isset($image_gallery) && is_array($image_gallery) && !empty($image_gallery)) ? array_column($image_gallery, 'date_change', 'api_id') : [];
                                        $saved_plans_images = (isset($plans_gallery) && is_array($plans_gallery) && !empty($plans_gallery)) ? array_column($plans_gallery, 'date_change', 'api_id') : [];

                                        if (isset($estate_details_array['images']) && !empty($estate_details_array['images'])) {

                                            // debug
                                            vitec_log('UPDATING IMAGES ON POST: ' . $existing_post_id);

                                            foreach ($estate_details_array['images'] as $image) {
                                                $image_category = (isset($image['category'])) ? $image['category'] : '';
                                                $image_text = (isset($image['text'])) ? $image['text'] : '';

                                                // images
                                                if (
                                                    strpos($image_category, 'Planritning') === false
                                                    && strpos($image_category, 'planritning') === false
                                                    && strpos($image_text, 'Planritning') === false
                                                    && strpos($image_text, 'planritning') === false
                                                ) {

                                                    // check if image exists in gallery and if it needs to be updated
                                                    if (isset($saved_gallery_images[$image['imageId']])) {
                                                        if ($force === true || strtotime($image['dateChanged']) > strtotime($saved_gallery_images[$image['imageId']])) {
                                                            $image_base = get_image_api($image['imageId']);
                                                            if ($image_base === false) {
                                                                $api_error = true;
                                                            }
                                                            if ($image_base) {
                                                                // get position of current attachment in image gallery array
                                                                $current_attachment_position = array_search($image['imageId'], array_column($image_gallery, 'api_id'));
                                                                $previous_attachment_id = $image_gallery[$current_attachment_position]['id'];

                                                                // debug
                                                                vitec_log('image-needs-update: ' . $image['imageId']);
                                                                vitec_log('deleting-image: ' . $previous_attachment_id);

                                                                // delete current attachment
                                                                wp_delete_attachment($previous_attachment_id, true);

                                                                // upload new attachment
                                                                $attachment_id = upload_base64_file(base64_encode($image_base), $image['imageId'], $existing_post_id);
                                                                if ($attachment_id) {

                                                                    // debug
                                                                    vitec_log('adding-image: ' . $attachment_id);

                                                                    // replace image data with new data on the same position
                                                                    $image_gallery[$current_attachment_position] = [
                                                                        'api_id' => $image['imageId'],
                                                                        'id' => $attachment_id,
                                                                        'date_change' => $image['dateChanged'],
                                                                        'order' => $image['orderNumber'],
                                                                        'show' => $image['showImageOnInternet']
                                                                    ];
                                                                    
                                                                    // delete leftover images from gallery - this is for scenario when there was an issue deleting old images
                                                                    if ($image_gallery) {
                                                                        foreach ($image_gallery as $key => $image_data) {
                                                                            if ($image_data['api_id'] == $image['imageId'] && $key != $current_attachment_position) {
                                                                                if ($image_data['id'] != $attachment_id) {
                                                                                    wp_delete_attachment($image_data['id'], true);
                                                                                }

                                                                                // debug
                                                                                vitec_log('deleting-leftover-image: ' . $image_data['id']);
                                                                                unset($image_gallery[$key]);
                                                                            }
                                                                        }
                                                                    }

                                                                    // delete leftover plans from gallery - this is for scenario when the image was moved from images gallery to plans gallery
                                                                    if ($plans_gallery) {
                                                                        foreach ($plans_gallery as $plans_key => $plans_image_data) {
                                                                            if ($plans_image_data['api_id'] == $image['imageId']) {
                                                                                if ($plans_image_data['id'] != $attachment_id) {
                                                                                    wp_delete_attachment($plans_image_data['id'], true);
                                                                                }

                                                                                // debug
                                                                                vitec_log('deleting-leftover-plan: ' . $plans_image_data['id']);
                                                                                unset($plans_gallery[$plans_key]);
                                                                            }
                                                                        }
                                                                    }

                                                                }
                                                            }
                                                        } else {

                                                            // debug
                                                            // vitec_log('skipping: image-dates-match');
                                                        }
                                                    }

                                                    // if image doesn't exist in gallery, add it
                                                    else {
                                                        $image_base = get_image_api($image['imageId']);
                                                        if ($image_base === false) {
                                                            $api_error = true;
                                                        }
                                                        if ($image_base) {
                                                            $attachment_id = upload_base64_file(base64_encode($image_base), $image['imageId'], $existing_post_id);
                                                            if ($attachment_id) {

                                                                // debug
                                                                vitec_log('adding-image: ' . $attachment_id);

                                                                $image_gallery[] = [
                                                                    'api_id' => $image['imageId'],
                                                                    'id' => $attachment_id,
                                                                    'date_change' => $image['dateChanged'],
                                                                    'order' => $image['orderNumber'],
                                                                    'show' => $image['showImageOnInternet']
                                                                ];

                                                                // debug
                                                                vitec_log('new-image-added: ' . $attachment_id);
                                                            }
                                                        }
                                                    }
                                                }

                                                // plans
                                                else {

                                                    // check if image exists in gallery or if it needs to be updated
                                                    if (isset($saved_plans_images[$image['imageId']])) {
                                                        if ($force === true || strtotime($image['dateChanged']) > strtotime($saved_plans_images[$image['imageId']])) {
                                                            $image_base = get_image_api($image['imageId']);
                                                            if ($image_base === false) {
                                                                $api_error = true;
                                                            }
                                                            if ($image_base) {
                                                                // get position of current attachment in plans gallery array
                                                                $current_attachment_position = array_search($image['imageId'], array_column($plans_gallery, 'api_id'));
                                                                $previous_attachment_id = $plans_gallery[$current_attachment_position]['id'];

                                                                // debug
                                                                vitec_log('plan-needs-update: ' . $image['imageId']);
                                                                vitec_log('deleting-plan: ' . $previous_attachment_id);

                                                                // delete current attachment
                                                                wp_delete_attachment($previous_attachment_id, true);

                                                                // upload new attachment
                                                                $attachment_id = upload_base64_file(base64_encode($image_base), $image['imageId'], $existing_post_id);
                                                                if ($attachment_id) {

                                                                    // debug
                                                                    vitec_log('adding-plan: ' . $attachment_id);

                                                                    // replace image data with new data on the same position
                                                                    $plans_gallery[$current_attachment_position] = [
                                                                        'api_id' => $image['imageId'],
                                                                        'id' => $attachment_id,
                                                                        'date_change' => $image['dateChanged'],
                                                                        'order' => $image['orderNumber'],
                                                                        'show' => $image['showImageOnInternet']
                                                                    ];

                                                                    // delete leftover plans from gallery - this is for scenario when there was an issue deleting old plans
                                                                    if ($plans_gallery) {
                                                                        foreach ($plans_gallery as $plans_key => $plans_image_data) {
                                                                            if ($plans_image_data['api_id'] == $image['imageId'] && $plans_key != $current_attachment_position) {
                                                                                if ($plans_image_data['id'] != $attachment_id) {
                                                                                    wp_delete_attachment($plans_image_data['id'], true);
                                                                                }

                                                                                // debug
                                                                                vitec_log('deleting-leftover-plan: ' . $plans_image_data['id']);
                                                                                unset($plans_gallery[$plans_key]);
                                                                            }
                                                                        }
                                                                    }

                                                                    // delete leftover images from gallery - this is for scenario when the image was moved from plans gallery to images gallery
                                                                    if ($image_gallery) {
                                                                        foreach ($image_gallery as $image_key => $image_data) {
                                                                            if ($image_data['api_id'] == $image['imageId']) {
                                                                                if ($image_data['id'] != $attachment_id) {
                                                                                    wp_delete_attachment($image_data['id'], true);
                                                                                }

                                                                                // debug
                                                                                vitec_log('deleting-leftover-image: ' . $image_data['id']);
                                                                                unset($image_gallery[$image_key]);
                                                                            }
                                                                        }
                                                                    }

                                                                }
                                                            }
                                                        } else {

                                                            // debug
                                                            // vitec_log('skipping: plan-dates-match');
                                                        }
                                                    }

                                                    // if image doesn't exist in gallery, add it
                                                    else {
                                                        $image_base = get_image_api($image['imageId']);
                                                        if ($image_base === false) {
                                                            $api_error = true;
                                                        }
                                                        if ($image_base) {
                                                            $attachment_id = upload_base64_file(base64_encode($image_base), $image['imageId'], $existing_post_id);
                                                            if ($attachment_id) {

                                                                // debug
                                                                vitec_log('adding-plan: ' . $attachment_id);

                                                                $plans_gallery[] = [
                                                                    'api_id' => $image['imageId'],
                                                                    'id' => $attachment_id,
                                                                    'date_change' => $image['dateChanged'],
                                                                    'order' => $image['orderNumber'],
                                                                    'show' => $image['showImageOnInternet']
                                                                ];

                                                                // debug
                                                                vitec_log('new-plan-added: ' . $attachment_id);
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            // delete images that are not in the API anymore
                                            if (is_array($image_gallery) && is_array($plans_gallery)) {
                                                $saved_gallery_images_ids = array_column($image_gallery, 'api_id');
                                                $saved_plans_images_ids = array_column($plans_gallery, 'api_id');
                                                $saved_images_ids = array_merge($saved_gallery_images_ids, $saved_plans_images_ids);
                                                $api_images_ids = array_column($estate_details_array['images'], 'imageId');
                                                $images_to_delete = array_diff($saved_images_ids, $api_images_ids);
                                                foreach ($images_to_delete as $image_id) {
                                                    // delete image from gallery
                                                    if (is_array($image_gallery) && in_array($image_id, $saved_gallery_images_ids)) {
                                                        $current_attachment_position = array_search($image_id, array_column($image_gallery, 'api_id'));
                                                        $previous_attachment_id = $image_gallery[$current_attachment_position]['id'];

                                                        // debug
                                                        vitec_log('deleting-image-not-in-api: ' . $previous_attachment_id);

                                                        wp_delete_attachment($previous_attachment_id, true);
                                                        unset($image_gallery[$current_attachment_position]);
                                                    }
                                                    // delete image from plans
                                                    if (is_array($plans_gallery) && in_array($image_id, $saved_plans_images_ids)) {
                                                        $current_attachment_position = array_search($image_id, array_column($plans_gallery, 'api_id'));
                                                        $previous_attachment_id = $plans_gallery[$current_attachment_position]['id'];

                                                        // debug
                                                        vitec_log('deleting-plan-not-in-api: ' . $previous_attachment_id);

                                                        wp_delete_attachment($previous_attachment_id, true);
                                                        unset($plans_gallery[$current_attachment_position]);
                                                    }
                                                }
                                            }

                                            // update gallery data
                                            if (is_array($image_gallery)) {
                                                
                                                // sort images by order
                                                usort($image_gallery, function ($a, $b) {
                                                    return $a['order'] <=> $b['order'];
                                                });
                                                
                                                // update gallery data
                                                update_field('gallery_data', $image_gallery, $existing_post_id);

                                            }

                                            if (is_array($plans_gallery)) {
                                                
                                                // sort plans by order
                                                usort($plans_gallery, function ($a, $b) {
                                                    return $a['order'] <=> $b['order'];
                                                });

                                                // update plans data
                                                update_field('plans_data', $plans_gallery, $existing_post_id);

                                            }
                                        }

                                        // documents
                                        if (isset($estate_details_array['advertiseOn']['documents']) && !empty($estate_details_array['advertiseOn']['documents'])) {

                                            // debug
                                            vitec_log('UPDATING DOCUMENTS ON POST: ' . $existing_post_id);

                                            $documents_list = get_field('documents', $existing_post_id);
                                            $saved_documents_list = (isset($documents_list) && is_array($documents_list) && !empty($documents_list)) ? array_column($documents_list, 'date_change', 'api_id') : [];
                                            foreach ($estate_details_array['advertiseOn']['documents'] as $document) {
                                                if (isset($saved_documents_list[$document['id']])) {
                                                    if ($force === true || strtotime($document['dateChangedData']) > strtotime($saved_documents_list[$document['id']])) {
                                                        $document_base = get_documents_api($document['id']);
                                                        if ($document_base === false) {
                                                            $api_error = true;
                                                        }
                                                        if ($document_base) {
                                                            $current_attachment_position = array_search($document['id'], array_column($documents_list, 'api_id'));
                                                            $previous_attachment_id = $documents_list[$current_attachment_position]['id'];

                                                            // debug
                                                            vitec_log('document-needs-update: ' . $document['id']);
                                                            vitec_log('deleting-document: ' . $previous_attachment_id);

                                                            // delete current attachment
                                                            wp_delete_attachment($previous_attachment_id, true);
                                                            // upload new attachment
                                                            $attachment_id = upload_base64_docs(base64_encode($document_base), $document['id'], $existing_post_id);

                                                            // debug
                                                            vitec_log('adding-document: ' . $attachment_id);

                                                            if ($attachment_id) {
                                                                // get position of current attachment in documents list array
                                                                // replace attachment data with new data on the same position
                                                                $documents_list[$current_attachment_position] = [
                                                                    'api_id' => $document['id'],
                                                                    'id' => $attachment_id,
                                                                    'name' => $document['name'],
                                                                    'date_change' => $document['dateChangedData'],
                                                                    'file' => $attachment_id
                                                                ];
                                                            }
                                                        }
                                                    } else {

                                                        // debug
                                                        // vitec_log('skipping: document-dates-match');
                                                    }
                                                } else {
                                                    $document_base = get_documents_api($document['id']);
                                                    if ($document_base === false) {
                                                        $api_error = true;
                                                    }
                                                    if ($document_base) {
                                                        $attachment_id = upload_base64_docs(base64_encode($document_base), $document['id'], $existing_post_id);
                                                        $documents_list[] = [
                                                            'api_id' => $document['id'],
                                                            'id' => $attachment_id,
                                                            'name' => $document['name'],
                                                            'date_change' => $document['dateChangedData'],
                                                            'file' => $attachment_id
                                                        ];

                                                        // debug
                                                        vitec_log('new-document-added: ' . $attachment_id);
                                                    }
                                                }
                                            }

                                            // delete documents that are not in the API anymore
                                            if (is_array($documents_list)) {
                                                $saved_documents_ids = array_column($documents_list, 'api_id');
                                                $api_documents_ids = array_column($estate_details_array['advertiseOn']['documents'], 'id');
                                                $documents_to_delete = array_diff($saved_documents_ids, $api_documents_ids);
                                                foreach ($documents_to_delete as $document_id) {
                                                    // delete document from gallery
                                                    if (in_array($document_id, $saved_documents_ids)) {
                                                        $current_attachment_position = array_search($document_id, array_column($documents_list, 'api_id'));
                                                        $previous_attachment_id = $documents_list[$current_attachment_position]['id'];

                                                        // debug
                                                        vitec_log('deleting-document-not-in-api: ' . $previous_attachment_id);

                                                        wp_delete_attachment($previous_attachment_id, true);
                                                        unset($documents_list[$current_attachment_position]);
                                                    }
                                                }
                                            }

                                            // update documents data
                                            update_field('documents', $documents_list, $existing_post_id);
                                        }
                                    }

                                    // finally, update the date change
                                    // if there was an API error, set date change to 01/01/1900 to force next update
                                    if ($api_error === false) {
                                        update_post_meta($existing_post_id, 'date_change', $estate['dateChanged']);
                                    } else {
                                        update_post_meta($existing_post_id, 'date_change', '01/01/1900 12:00 am');
                                    }

                                    // clear cache
                                    if (function_exists('rocket_clean_post')) {
                                        rocket_clean_post(64); // Hem
                                        rocket_clean_post(130); // Till salu

                                        if (isset($estate_details_array['assignment']['responsibleBroker']) && !empty($estate_details_array['assignment']['responsibleBroker'])) {
                                            $user_id = get_team_id_offer($estate_details_array['assignment']['responsibleBroker']);
                                            if ($user_id) {
                                                rocket_clean_post($user_id); // Broker
                                            }
                                        }
                                    }

                                    // debug
                                    if ($api_error === false) {
                                        vitec_log('POST ' . $existing_post_id . ' SUCCESSFULLY UPDATED');
                                    } else {
                                        vitec_log('POST ' . $existing_post_id . ' UPDATED WITH ERRORS');
                                    }
                                }
                            }
                        }

                        // if post doesn't exist, create it
                        else {

                            // details in json
                            $estate_details = get_estate_details($estate['id'], $estate_type_name);
                            if ($estate_details === false) {
                                $api_error = true;
                            }
                            
                            if ($estate_details) {
                                $estate_details_array = json_decode($estate_details, true);

                                // prepare post data
                                $post_data = [
                                    'post_title'    => wp_strip_all_tags($estate['streetAddress']), // Using street address as title
                                    'post_content'  => '',
                                    'post_status'   => 'publish',
                                    'post_type'     => 'houses',
                                ];
                                $post_data['meta_input'] = [
                                    'house_id'      => $estate['id'],
                                    'status'        => $estate['status']['name'],
                                    'status_id'     => $status_id,
                                    'livingspace'   => $estate['livingSpace'],
                                    'rooms'         => $estate['rooms'],
                                    'price'         => $estate['price'],
                                    'address'       => $estate['streetAddress'],
                                    'area'          => $estate['areaName'],
                                    'municipality'  => $estate_details_array['baseInformation']['objectAddress']['municipality'],
                                    'date_change'   => $estate['dateChanged'],
                                    'property_type' => $estate_type_name,
                                    'json_data'     => wp_slash($estate_details),
                                ];

                                // publish/unpublish
                                if (in_array($estate['status']['id'], ['3', '10'])) {
                                    // if status is Till salu or Såld/Referensobjekt, check homepage field
                                    if (isset($estate_details_array['advertiseOn']['homepage'])) {
                                        if ($estate_details_array['advertiseOn']['homepage'] == true) {
                                            $post_data['post_status'] = 'publish';
                                        } else {
                                            $post_data['post_status'] = 'upcoming';
                                        }
                                    }
                                } elseif ($estate['status']['id'] == '2') {
                                    // if status is Kommande, check showAsComming field
                                    if (isset($estate_details_array['advertiseOn']['showAsComming'])) {
                                        if ($estate_details_array['advertiseOn']['showAsComming'] == true) {
                                            $post_data['post_status'] = 'publish';
                                        } else {
                                            $post_data['post_status'] = 'upcoming';
                                        }
                                    }
                                }

                                // create post
                                $post_id = wp_insert_post($post_data);

                                // debug
                                vitec_log('ADDING OBJECT: ' . $estate['id'] . ' AS POST: ' . $post_id);

                                // set post thumbnail
                                if (isset($estate['mainImage']['imageId']) && !empty($estate['mainImage']['imageId'])) {

                                    // check if thumbnail is a floor plan
                                    $is_plan = false;
                                    if (isset($estate_details_array['images']) && !empty($estate_details_array['images'])) {
                                        $image_row = array_filter($estate_details_array['images'], function ($item) use ($estate) {
                                            return $item['imageId'] == $estate['mainImage']['imageId'];
                                        });
                                        if (
                                            strpos($image_row[0]['category'], 'Planritning') !== false
                                            || strpos($image_row[0]['category'], 'planritning') !== false
                                            || strpos($image_row[0]['text'], 'Planritning') !== false
                                            || strpos($image_row[0]['text'], 'planritning') !== false
                                        ) {
                                            $is_plan = true;
                                        }
                                    }

                                    // thumbnail data
                                    $thumbnail_data = [];

                                    $image_base = get_image_api($estate['mainImage']['imageId']);
                                    if ($image_base === false) {
                                        $api_error = true;
                                    }
                                    if ($image_base) {
                                        $attachment_id = upload_base64_file(base64_encode($image_base), $estate['mainImage']['imageId'] . '_thumb', $post_id);
                                        if ($attachment_id) {
                                            set_post_thumbnail($post_id, $attachment_id);

                                            // save thumbnail data
                                            $thumbnail_data[] = [
                                                'api_id' => $estate['mainImage']['imageId'],
                                                'id' => $attachment_id,
                                                'date_change' => $estate['mainImage']['dateChanged'],
                                                'is_plan' => $is_plan,
                                                'show' => $estate['mainImage']['showImageOnInternet']
                                            ];

                                            // debug
                                            vitec_log('new-thumbnail-added: ' . $attachment_id);

                                            update_field('thumbnail_data', $thumbnail_data, $post_id);
                                        }
                                    }
                                }

                                if ($update_media) {

                                    // galleries
                                    $estate_details_array = json_decode($estate_details, true);
                                    $image_gallery = [];
                                    $plans_gallery = [];

                                    if (isset($estate_details_array['images']) && !empty($estate_details_array['images'])) {
                                        foreach ($estate_details_array['images'] as $image) {
                                            $image_category = (isset($image['category'])) ? $image['category'] : '';
                                            $image_text = (isset($image['text'])) ? $image['text'] : '';

                                            // images
                                            if (
                                                strpos($image_category, 'Planritning') === false
                                                && strpos($image_category, 'planritning') === false
                                                && strpos($image_text, 'Planritning') === false
                                                && strpos($image_text, 'planritning') === false
                                            ) {
                                                $image_base = get_image_api($image['imageId']);
                                                if ($image_base === false) {
                                                    $api_error = true;
                                                }
                                                if ($image_base) {
                                                    $attachment_id = upload_base64_file(base64_encode($image_base), $image['imageId'], $post_id);
                                                    if ($attachment_id) {
                                                        $image_gallery[] = [
                                                            'api_id' => $image['imageId'],
                                                            'id' => $attachment_id,
                                                            'date_change' => $image['dateChanged'],
                                                            'order' => $image['orderNumber'],
                                                            'show' => $image['showImageOnInternet']
                                                        ];

                                                        // debug
                                                        vitec_log('new-image-added: ' . $attachment_id);
                                                    }
                                                }
                                            }

                                            // plans
                                            else {
                                                $image_base = get_image_api($image['imageId']);
                                                if ($image_base === false) {
                                                    $api_error = true;
                                                }
                                                if ($image_base) {
                                                    $attachment_id = upload_base64_file(base64_encode($image_base), $image['imageId'], $post_id);
                                                    if ($attachment_id) {
                                                        $plans_gallery[] = [
                                                            'api_id' => $image['imageId'],
                                                            'id' => $attachment_id,
                                                            'date_change' => $image['dateChanged'],
                                                            'order' => $image['orderNumber'],
                                                            'show' => $image['showImageOnInternet']
                                                        ];

                                                        // debug
                                                        vitec_log('new-plan-added: ' . $attachment_id);
                                                    }
                                                }
                                            }
                                        }

                                        // save gallery data
                                        if (is_array($image_gallery)) {
                                            update_field('gallery_data', $image_gallery, $post_id);
                                        }
                                        if (is_array($plans_gallery)) {
                                            update_field('plans_data', $plans_gallery, $post_id);
                                        }
                                    }


                                    // documents
                                    if (isset($estate_details_array['advertiseOn']['documents']) && !empty($estate_details_array['advertiseOn']['documents'])) {
                                        $documents_list = [];
                                        foreach ($estate_details_array['advertiseOn']['documents'] as $document) {
                                            $document_base = get_documents_api($document['id']);
                                            if ($document_base === false) {
                                                $api_error = true;
                                            }
                                            if ($document_base) {
                                                $attachment_id = upload_base64_docs(base64_encode($document_base), $document['id'], $post_id);
                                                $documents_list[] = [
                                                    'api_id' => $document['id'],
                                                    'id' => $attachment_id,
                                                    'name' => $document['name'],
                                                    'date_change' => $document['dateChangedData'],
                                                    'file' => $attachment_id
                                                ];
                                                
                                                // debug
                                                vitec_log('new-document-added: ' . $attachment_id);
                                            }
                                        }
                                        update_field('documents', $documents_list, $post_id);
                                    }
                                }

                                // if there was an API error, set date change to 01/01/1900 to force next update
                                if ($api_error) {
                                    update_post_meta($post_id, 'date_change', '01/01/1900 12:00 am');
                                }

                                // clear cache
                                if (function_exists('rocket_clean_post')) {
                                    rocket_clean_post(64); // Hem
                                    rocket_clean_post(130); // Till salu

                                    if (isset($estate_details_array['assignment']['responsibleBroker']) && !empty($estate_details_array['assignment']['responsibleBroker'])) {
                                        $user_id = get_team_id_offer($estate_details_array['assignment']['responsibleBroker']);
                                        if ($user_id) {
                                            rocket_clean_post($user_id); // Broker
                                        }
                                    }
                                }

                                // debug
                                vitec_log('NEW OBJECT ' . $estate['id'] . ' SUCCESSFULLY ADDED WITH POST ID: ' . $post_id);
                            }
                        }
                    }
                }
            }
        }

        // Trash posts that are not in the API anymore
        if ($trash_old === true && is_countable($api_estates_ids) && count($api_estates_ids) > 100) {
            $posts_to_trash = get_posts([
                'post_type' => 'houses',
                'posts_per_page' => -1,
                'post_status' => ['publish', 'upcoming'],
                'fields' => 'ids',
                'meta_query' => [
                    [
                        'key' => 'house_id',
                        'compare' => 'NOT IN',
                        'value' => $api_estates_ids,
                    ],
                ],
            ]);
            if ($posts_to_trash) {
                foreach ($posts_to_trash as $post_id) {
                    // debug
                    vitec_log('MOVING POST TO TRASH: ' . $post_id);

                    // move post to trash
                    wp_update_post([
                        'ID'            => $post_id,
                        'post_status'   => 'trash',
                    ]);
                }

                // clear cache
                if (function_exists('rocket_clean_post')) {
                    rocket_clean_post(64); // Hem
                    rocket_clean_post(130); // Till salu
                }
            }
        }
    } else {
        // save information to options table that the update is done
        update_option('vitec_update_running', false);

        // log error
        vitec_log('ERROR: Could not retrieve estates from API');

        // error response
        return 0;
    }

    // save information to options table that the update is done
    update_option('vitec_last_update', wp_date('Y-m-d H:i:s'));
    update_option('vitec_update_running', false);
    
    // debug
    vitec_log('UPDATE COMPLETED');

    // success
    return 1;
}

/**
 * Retrieves the details of an estate based on the estate ID and estate type.
 *
 * @param string $estate_id The ID of the estate.
 * @param string $estate_type The type of the estate (houses, cottages, housingCooperativeses, plots, projects, farms, commercialPropertys, condominiums, foreignProperties, premises).
 * @return mixed The details of the estate or false.
 */
function get_estate_details($estate_id, $estate_type) {
    switch ($estate_type) {
        case 'houses':
            $endpoint = "Estate/GetHouse";
            break;
        case 'cottages':
            $endpoint = "Estate/GetCottage";
            break;
        case 'housingCooperativeses':
            $endpoint = "Estate/GetHousingCooperative";
            break;
        case 'plots':
            $endpoint = "Estate/GetPlot";
            break;
        case 'projects':
            $endpoint = "Estate/GetProject";
            break;
        case 'farms':
            $endpoint = "Estate/GetFarm";
            break;
        case 'commercialPropertys':
            $endpoint = "Estate/GetCommercialProperty";
            break;
        case 'condominiums':
            $endpoint = "Estate/GetCondominium";
            break;
        case 'foreignProperties':
            $endpoint = "Estate/GetForeignProperty";
            break;
        case 'premises':
            $endpoint = "Estate/GetPremises";
            break;
        default:
    }

    $url = "https://connect.maklare.vitec.net/" . $endpoint . "/?estateId=" . $estate_id . "&customerId=M22127&onlyFutureViewings=False";
    $username = "670";
    $password = "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS";
    $ch = curl_init();
    
    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4
    
    curl_setopt($ch, CURLOPT_USERNAME, $username);
    curl_setopt($ch, CURLOPT_PASSWORD, $password);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $url);

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if ($http_code != 200) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - get_estate_details - ' . $error . ' - Estate ID: ' . $estate_id);
        return false;
    }

    return $result;
}

/**
 * Retrieves viewings from the API by property ID.
 * 
 * @param string $estate_id The ID of the estate.
 * @return string The result of the API request.
 */
function get_viewings_api($estate_id) {
    $URL = "https://connect.maklare.vitec.net/Viewing/M22127/" . $estate_id;
    $ch = curl_init();

    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4

    curl_setopt($ch, CURLOPT_USERNAME, "670");
    curl_setopt($ch, CURLOPT_PASSWORD, "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_BINARYTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $URL);

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if ($http_code != 200) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - get_viewings_api - ' . $error . ' - Estate ID: ' . $estate_id);
        return false;
    }

    return $result;
}

/**
 * Retrieves an image from the API based on the provided image ID.
 *
 * @param int $image_id The ID of the image to retrieve.
 * @return string The result of the API request.
 */
function get_image_api($image_id) {
    $URL = "https://connect.maklare.vitec.net/Image/GetImage?customerId=M22127&imageId=" . $image_id . "&w=1920&mode=max";
    $ch = curl_init();

    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4

    curl_setopt($ch, CURLOPT_USERNAME, "670");
    curl_setopt($ch, CURLOPT_PASSWORD, "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_BINARYTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $URL);

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if ($http_code != 200) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Check for empty or invalid response
    if (empty($result) || strlen($result) < 100) { // Minimum size check (100 bytes)
        $error = "Response Error: Empty or invalid image response";
    }

    // Check if response starts with known image signatures (JPEG, PNG, or GIF headers)
    if (!$error && !preg_match('/^\xFF\xD8|\x89\x50\x4E\x47|\x47\x49\x46\x38/i', $result)) {
        $error = "Response Error: Invalid image format";
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - get_image_api for image ' . $image_id . ' - ' . $error);
        return false;
    }

    return $result;
}

/**
 * Retrieves documents from the API based on the given file ID.
 *
 * @param string $file_id The ID of the file to retrieve.
 * @return string The response from the API.
 */
function get_documents_api($file_id) {
    $URL = "https://connect.maklare.vitec.net/File/GetFile?customerId=M22127&fileId=" . $file_id;

    $ch = curl_init();

    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4

    curl_setopt($ch, CURLOPT_USERNAME, "670");
    curl_setopt($ch, CURLOPT_PASSWORD, "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_BINARYTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $URL);

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if ($http_code != 200) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Check for empty or invalid response
    if (empty($result) || strlen($result) < 100) { // Minimum size check (100 bytes)
        $error = "Response Error: Empty or invalid file response";
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - get_documents_api for file ' . $file_id . ' - ' . $error);
        return false;
    }

    return $result;
}

/**
 * Uploads a base64 encoded file to the WordPress media library.
 *
 * @param string $base64_file The base64 encoded file.
 * @param string $file_name The name of the file.
 * @param int|null $parent_id The ID of the parent post (optional).
 * @return int|false The ID of the uploaded attachment on success, false on failure.
 */
function upload_base64_docs($base64_file, $file_name, $parent_id = null) {
    $upload_dir = wp_upload_dir();
    $upload_path = str_replace('/', DIRECTORY_SEPARATOR, $upload_dir['path']) . DIRECTORY_SEPARATOR;

    $decoded_file = base64_decode($base64_file);
    $file_name = $parent_id . '_' . $file_name . '.pdf';

    $uploaded_file = file_put_contents($upload_path . $file_name, $decoded_file);

    if ($uploaded_file !== false) {
        $attachment = [
            'post_mime_type' => 'application/pdf',
            'post_title' => sanitize_file_name($file_name),
            'post_content' => '',
            'post_status' => 'publish',
            'post_parent' => $parent_id
        ];
        $attach_id = wp_insert_attachment($attachment, $upload_path . $file_name);

        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attach_data = wp_generate_attachment_metadata($attach_id, $upload_path . $file_name);
        wp_update_attachment_metadata($attach_id, $attach_data);

        return $attach_id;
    }

    return false;
}

/**
 * Uploads a base64 encoded file to the WordPress media library.
 *
 * @param string $base64_file The base64 encoded file.
 * @param string $file_name The name of the file.
 * @param int|null $parent_id The ID of the parent post (optional).
 * @return int|false The attachment ID if successful, false otherwise.
 */
function upload_base64_file($base64_file, $file_name, $parent_id = null) {
    $upload_dir = wp_upload_dir();
    $upload_path = str_replace('/', DIRECTORY_SEPARATOR, $upload_dir['path']) . DIRECTORY_SEPARATOR;

    $decoded_file = base64_decode($base64_file);
    $file_name = $parent_id . '_' . $file_name . '.jpg';

    $uploaded_file = file_put_contents($upload_path . $file_name, $decoded_file);

    if ($uploaded_file !== false) {
        $attachment = [
            'post_mime_type' => 'image/jpg',
            'post_title' => sanitize_file_name($file_name),
            'post_content' => '',
            'post_status' => 'publish',
            'post_parent' => $parent_id
        ];
        $attach_id = wp_insert_attachment($attachment, $upload_path . $file_name);

        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attach_data = wp_generate_attachment_metadata($attach_id, $upload_path . $file_name);
        wp_update_attachment_metadata($attach_id, $attach_data);

        return $attach_id;
    }

    return false;
}

/**
 * Retrieves all houses from the Vitec API (post IDs and Vitec API IDs in one call).
 *
 * @return string The result of the API request.
 */
function get_all_estates_with_ids() {
    global $wpdb;
    $query = $wpdb->prepare(
        "SELECT p.ID, pm.meta_value AS house_id
        FROM $wpdb->posts p
        INNER JOIN $wpdb->postmeta pm ON p.ID = pm.post_id
        WHERE p.post_type = %s
        AND pm.meta_key = %s",
        'houses',
        'house_id'
    );
    $results = $wpdb->get_results($query);
    return $results;
}

/**
 * Deletes estates data.
 *
 * This function is responsible for deleting houses and attachments from the Vitec API.
 * If the $remove_old parameter is set to false, it only deletes attachments created by the new update script.
 * If the $remove_old parameter is set to true, it deletes all attachments and also deletes old option tables.
 *
 * @param bool $remove_old Whether to remove old attachments and option tables. Default is false.
 * @return void
 */
function delete_estates_data($remove_old = false) {

    // delete all houses
    $args = [
        'post_type' => 'houses',
        'posts_per_page' => -1,
        'post_status' => ['publish', 'upcoming', 'pending', 'draft', 'auto-draft', 'future', 'private', 'inherit', 'trash']
    ];
    $houses = get_posts($args);
    
    foreach ($houses as $house) {

        // delete attachments from Vitec API (created by new update script)
        if ($remove_old === false) {
            $args = [
                'post_type' => 'attachment',
                'posts_per_page' => -1,
                'post_status' => 'any',
                'post_parent' => $house->ID,
                'fields' => 'ids'
            ];
            $attachments = get_posts($args);
            foreach ($attachments as $attachment_id) {
                wp_delete_attachment($attachment_id, true);
            }
        }

        wp_delete_post($house->ID, true);
    }

    // delete attachments from Vitec API (created by old update script)
    if ($remove_old !== false) {

        // delete all attachments
        $args = [
            'post_type' => 'attachment',
            'posts_per_page' => -1,
            'post_status' => 'any'
        ];
        $attachments = get_posts($args);
        foreach ($attachments as $attachment) {
            $attachment_name = get_the_title($attachment->ID);
            if (strpos($attachment_name, 'image_') === 0 || strpos($attachment_name, 'doc_') === 0) {
                wp_delete_attachment($attachment->ID, true);
            }
        }

        // delete old option tables
        delete_option('wm_houses_json');
        delete_option('wm_houses_json_hash');
        delete_option('wm_houses_json_time');
        delete_option('wm_search_suggestions');
    }
}

/**
 * Retrieves the statuses from the API.
 *
 * @return string The response from the API.
 */
function get_statuses() {
    $URL = "https://connect.maklare.vitec.net/Estate/GetStatuses?customerId=M22127";
    $ch = curl_init();

    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4

    curl_setopt($ch, CURLOPT_USERNAME, "670");
    curl_setopt($ch, CURLOPT_PASSWORD, "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $URL);
    curl_setopt($ch, CURLOPT_POST, true);


    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if ($http_code != 200) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - get_statuses - ' . $error);
        return false;
    }

    return $result;
}

/**
 * Sends contact information to the house API.
 *
 * @param array $data_form The contact form data.
 * @return string The response from the API.
 */
function send_contact_to_house($data_form) {
    $URL = "https://connect.maklare.vitec.net/Interest/SendInterestAndContactOnEstate";
    $ch = curl_init();

    // debug
    vitec_log('send_contact_to_house - request URL: ' . $URL, 'contact');

    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4

    curl_setopt($ch, CURLOPT_USERNAME, "670");
    curl_setopt($ch, CURLOPT_PASSWORD, "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $URL);

    $data = json_encode($data_form);

    // debug
    vitec_log('send_contact_to_house - data: ' . $data, 'contact');

    curl_setopt($ch, CURLOPT_POST, true);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data)
    ));

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if (!in_array($http_code, [200, 204])) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - send_contact_to_house - ' . $error, 'contact');
        return false;
    }

    // debug
    vitec_log('send_contact_to_house - response: ' . $result, 'contact');

    return $result;
}

/**
 * Add participant to specific viewing timeslot in Vitec.
 *
 * @param array $data_form The body data.
 * @return string The response from the API.
 */
function add_viewing_timeslot_participant($viewing_id, $timeslot_id, $data_form) {
    $URL = "https://connect.maklare.vitec.net/Viewing/M22127/" . $viewing_id . "/" . $timeslot_id . "/Participant";
    $ch = curl_init();

    // debug
    vitec_log('add_viewing_timeslot_participant - request URL: ' . $URL, 'viewing');

    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4

    curl_setopt($ch, CURLOPT_USERNAME, "670");
    curl_setopt($ch, CURLOPT_PASSWORD, "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $URL);

    $data = json_encode($data_form);

    // debug
    vitec_log('add_viewing_timeslot_participant - data: ' . $data, 'viewing');

    curl_setopt($ch, CURLOPT_POST, true);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data)
    ));

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if ($http_code != 200) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - add_viewing_timeslot_participant - ' . $error, 'viewing');
        return false;
    }

    // debug
    vitec_log('add_viewing_timeslot_participant - response: ' . $result, 'viewing');

    return $result;
}

/**
 * Retrieves all users from the Vitec API.
 *
 * @return string The response from the API containing the user data.
 */
function get_all_users() {
    $URL = "https://connect.maklare.vitec.net/User/GetAllUsers?CustomerId=M22127";

    $ch = curl_init();

    // Add timeout settings
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);                  // Maximum time the request is allowed to take
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);           // Maximum time allowed for connection
    curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, 120);       // DNS cache timeout
    curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // Force IPv4

    curl_setopt($ch, CURLOPT_USERNAME, "670");
    curl_setopt($ch, CURLOPT_PASSWORD, "Vb44i6L3QVGhLVRIecCrgMIXSMB4Hv7BySXYyILeh53s0Ix8TP59fPi9IVZKtGjS");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, $URL);

    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    $error = false;

    if (curl_errno($ch)) {
        // Log CURL errors
        $error = "CURL Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")";
        curl_close($ch);
    } else {
        curl_close($ch);
        // Log HTTP error codes
        if ($http_code != 200) {
            $error = "HTTP Error: " . $http_code;
        }
    }

    // Log API errors
    if ($error !== false) {
        vitec_log('ERROR - get_all_users - ' . $error);
        return false;
    }

    return $result;
}

/**
 * Logs a message to a Vitec debug log file.
 *
 * @param string $message The message to be logged.
 * @param string $type The type of the message (default is 'debug').
 * @return void
 */
function vitec_log($message, $type = 'debug') {
    switch ($type) {
        case 'debug':
            $file_name = 'vitec_debug.log';
            break;
        case 'contact':
            $file_name = 'vitec_contact.log';
            break;
        case 'viewing':
            $file_name = 'vitec_viewing.log';
            break;
        default:
            $file_name = 'vitec_debug.log';
    }
    $path = WP_CONTENT_DIR . DIRECTORY_SEPARATOR . $file_name;
    $content = '';
    if (file_exists($path)) {
        $content .= file_get_contents($path);
    }
    $content .= "\n" . '[' . date('Y-m-d H:i:s') . '] ' . $message;
    file_put_contents($path, $content);
}

/**
 * Checks if the current website is in production mode.
 *
 * @return bool Returns true if the current website is in production mode, false otherwise.
 */
function is_production() {
    $current_url = get_site_url();
    if (
        strpos($current_url, 'bergetsro.se') !== false
        || strpos($current_url, 'bergetsro.vmate.se') !== false
    ) {
        return true;
    }
    return false;
}

/**
 * Add redirects for old Vitec URLs.
 */
function vitec_redirects() {
    // I need to take this URL: /Beskrivning/OBJ22127_2034894126 - take the OBJ22127_2034894126 and use it to get the house ID from the database (custom field), then redirect to the new URL.
    if (isset($_SERVER['REQUEST_URI'])) {
        // Get the current URL
        $url = $_SERVER['REQUEST_URI'];

        // Extract the object ID from the URL
        preg_match('/OBJ\d+_\d+/', $url, $matches);
        $objectId = isset($matches[0]) ? $matches[0] : '';

        // Check if the object ID exists
        if (!empty($objectId)) {
            $args = [
                'post_type'  => 'houses',
                'posts_per_page' => 1,
                'post_status' => 'any',
                'fields' => 'ids',
                'meta_query' => [
                    [
                        'key'   => 'house_id',
                        'value' => $objectId,
                    ],
                ],
            ];

            $houses = get_posts($args);

            // If post found, redirect to its URL
            if ($houses) {
                $house_url = get_permalink($houses[0]);
                wp_redirect($house_url, 301); // 301 redirect for permanent redirect
                exit;
            }
        }
    }
}
add_action('init', 'vitec_redirects');

/**
 * Update status field based on json data.
 */
function update_status_field($post_id) {
    $json_data = get_field('json_data', $post_id);
    if ($json_data) {
        $json_data = json_decode($json_data, true);
        if (isset($json_data['status']['id'])) {
            $status_id = intval($json_data['status']['id']);
            update_field('status_id', $status_id, $post_id);
        }
    }
}

