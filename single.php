<?php
get_header();

while (have_posts()) :
    the_post();

    // contact form shortcode
    $fields_option = get_field('single_offer', 'options');

    // get house details from global variable
    $house_details = isset($GLOBALS['house_details']) ? $GLOBALS['house_details'] : [];
    $status = (isset($house_details['assignment']['status']['id'])) ? intval($house_details['assignment']['status']['id']) : 0;

    // get property type
    // possible values: "houses", "cottages", "housingCooperativeses", "plots", "projects", "farms", "commercialPropertys", "condominiums", "foreignProperties", "premises"
    // client currently uses "houses", "housingCooperativeses" and "condominiums" as property types
    $property_type = get_field('property_type');

    // update the status in case it has changed
    if ($status) {
        update_field('status_id', $status);
    }

    // get images and plans from global variable
    $gallery_data = isset($GLOBALS['gallery_data']) ? $GLOBALS['gallery_data'] : [];
    $plans_data = isset($GLOBALS['plans_data']) ? $GLOBALS['plans_data'] : [];

    // sort gallery images by order
    if (is_array($gallery_data) && !empty($gallery_data)) {
        usort($gallery_data, function ($a, $b) {
            return $a['order'] - $b['order'];
        });
    }

    // sort plans by order
    if (is_array($plans_data) && !empty($plans_data)) {
        usort($plans_data, function ($a, $b) {
            return $a['order'] - $b['order'];
        });
    }

    // remove images where show is not set to true
    if (is_array($gallery_data) && !empty($gallery_data)) {
        $gallery_data = array_filter($gallery_data, function ($item) {
            return $item['show'] == true;
        });
    }

    // remove plans where show is not set to true
    if (is_array($plans_data) && !empty($plans_data)) {
        $plans_data = array_filter($plans_data, function ($item) {
            return $item['show'] == true;
        });
    }

    // put gallery images in slider
    if (is_array($gallery_data)) {
        $slider_images = array_column($gallery_data, 'id');
    } else {
        $slider_images = [];
    }

    // detect if thumbnail is a floor plan or not
    $post_thumbnail_id = (isset($thumbnail_data[0]['id'])) ? $thumbnail_data[0]['id'] : get_post_thumbnail_id();
    $thumbnail_data = get_field('thumbnail_data');
    $thumbnail_is_plan = (isset($thumbnail_data[0]['is_plan']) && $thumbnail_data[0]['is_plan'] == true) ? true : false;

    // add thumbnail to slider as first element (if not already there)
    if (
        isset($thumbnail_data[0]['id'])
        && is_array($thumbnail_data[0]['id'])
        && isset($thumbnail_data[0]['api_id'])
        && is_array($thumbnail_data[0]['api_id'])
        && !in_array($thumbnail_data[0]['api_id'], array_column($gallery_data, 'api_id'))
        && !in_array($thumbnail_data[0]['api_id'], array_column($plans_data, 'api_id'))
    ) {
        array_unshift($slider_images, $thumbnail_data[0]['id']);
    }

    // create multidimensional array from slider_images to also add url and orientation
    if (!empty($slider_images)) {
        $slider_images = array_map(function ($item) {
            $image_src = wp_get_attachment_image_src($item, 'hero-bg');
            $image_url = (isset($image_src[0])) ? $image_src[0] : '';
            $image_width = (isset($image_src[1])) ? $image_src[1] : '';
            $image_height = (isset($image_src[2])) ? $image_src[2] : '';
            $image_orientation = ($image_width > $image_height) ? 'landscape' : 'portrait';
            return [
                'id' => $item,
                'url' => $image_url,
                'orientation' => $image_orientation,
                'width' => $image_width,
                'height' => $image_height
            ];
        }, $slider_images);

        // remove duplicates
        $slider_images_urls = array_column($slider_images, 'url');
        $slider_images = array_intersect_key($slider_images, array_unique($slider_images_urls));
    }

    // object details
    if ($property_type == 'houses') :
        include_once('templates/single/houses.php');
    elseif ($property_type == 'condominiums') :
        include_once('templates/single/condominiums.php');
    else :
        include_once('templates/single/housing-cooperativeses.php');
    endif;

    // debug
    // print('<pre style="font-size: 15px">');
    // print_r($house_details);
    // print('</pre>');

    // broker details
    if (isset($house_details['assignment']['responsibleBroker']) && !empty($house_details['assignment']['responsibleBroker'])) :
        $user_id = get_team_id_offer($house_details['assignment']['responsibleBroker']);
        if ($user_id) :
        ?>
            <section class="vm-contact vm-contact--team" id="contact">
                <div class="vm-grid">
                    <?php 
                    if (isset($house_details['assignment']['additionalContact']) && !empty($house_details['assignment']['additionalContact'])) :
                        $additional_user_id = get_team_id_offer($house_details['assignment']['additionalContact']);
                        if ($additional_user_id) :
                        ?>
                        <div class="vm-col-12 vm-col-lg-6 vm-col-xl-6 vm-col-space-between-v">
                            <div class="vm-contact-team">
                                <div class="vm-contact-team__double">
                                    <div class="vm-col vm-col-align-center">
                                        <a href="<?php echo get_permalink($user_id); ?>" class="vm-contact-team__image link" style="background-image: url(<?php echo get_the_post_thumbnail_url($user_id, 'large') ?>)"></a>
                                        <div class="vm-contact-team__meta">
                                            <small><?php echo get_the_title($user_id); ?></small>
                                            <small><?php
                                                    $terms = get_the_terms($user_id, 'profession');
                                                    $profession = (isset($terms[0])) ? $terms[0]->name : '';
                                                    $profession_text = '';
                                                    if (!empty($profession) && strpos($profession, 'Fastighetsmäklare') !== false) {
                                                        $profession_text .= __('Reg.', TEXTDOMAIN) . ' ';
                                                    }
                                                    $profession_text .= $profession;
                                                    echo esc_html($profession_text);
                                                    ?></small>
                                            <?php if (get_field('reg_number', $user_id)) { ?>
                                                <small><a href="tel:<?php echo str_replace(' ', '', get_field('reg_number', $user_id)); ?>"><?php echo get_field('reg_number', $user_id); ?></a></small>
                                            <?php } ?>
                                            <?php if (get_field('email', $user_id)) { ?>
                                                <small><a href="mailto:<?php echo trim(get_field('email', $user_id)); ?>"><?php echo trim(get_field('email', $user_id)); ?></a></small>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div class="vm-col vm-col-align-center">
                                        <a href="<?php echo get_permalink($additional_user_id); ?>" class="vm-contact-team__image link" style="background-image: url(<?php echo get_the_post_thumbnail_url($additional_user_id, 'large') ?>)"></a>
                                        <div class="vm-contact-team__meta">
                                            <small style="font-style: italic"><?php esc_html_e('Assisterande fastighetsmäklare', TEXTDOMAIN); ?></small>
                                            <small><?php echo get_the_title($additional_user_id); ?></small>
                                            <small><?php
                                                    $terms = get_the_terms($additional_user_id, 'profession');
                                                    $profession = (isset($terms[0])) ? $terms[0]->name : '';
                                                    $profession_text = '';
                                                    if (!empty($profession) && strpos($profession, 'Fastighetsmäklare') !== false) {
                                                        $profession_text .= __('Reg.', TEXTDOMAIN) . ' ';
                                                    }
                                                    $profession_text .= $profession;
                                                    echo esc_html($profession_text);
                                                    ?></small>
                                            <?php if (get_field('reg_number', $additional_user_id)) { ?>
                                                <small><a href="tel:<?php echo str_replace(' ', '', get_field('reg_number', $additional_user_id)); ?>"><?php echo get_field('reg_number', $additional_user_id); ?></a></small>
                                            <?php } ?>
                                            <?php if (get_field('email', $additional_user_id)) { ?>
                                                <small><a href="mailto:<?php echo trim(get_field('email', $additional_user_id)); ?>"><?php echo trim(get_field('email', $additional_user_id)); ?></a></small>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="vm-col-12 vm-col-lg-6 vm-col-xl-6">
                            <div class="vm-contact__form">
                                <?php if ($timeslots_i == 0) { ?>
                                    <style>
                                        .vm-contact__form label[for="select-date"] {
                                            display: none;
                                        }
                                    </style>
                                <?php }
                                if (isset($house_details['assignment']['status']['id']) && $house_details['assignment']['status']['id'] == 10) {
                                    $shortcode = $fields_option['contact_form_sold'];
                                } else {
                                    $shortcode = $fields_option['contact_form'];
                                }
                                if ($shortcode) :
                                    echo do_shortcode($shortcode);
                                endif;
                                ?>
                            </div>
                        </div>
                        <?php 
                        endif;
                    else : 
                    ?>
                    <div class="vm-col-12 vm-col-md-5 vm-col-xl-6 vm-col-space-between-v">
                        <div class="vm-contact-team">
                            <div class="vm-grid">
                                <div class="vm-col-12 vm-col-align-center">
                                    <a href="<?php echo get_permalink($user_id); ?>" class="vm-contact-team__image link" style="background-image: url(<?php echo get_the_post_thumbnail_url($user_id, 'large') ?>)"></a>
                                    <div class="vm-contact-team__meta">
                                        <small><?php echo get_the_title($user_id); ?></small>
                                        <small><?php
                                                $terms = get_the_terms($user_id, 'profession');
                                                $profession = (isset($terms[0])) ? $terms[0]->name : '';
                                                $profession_text = '';
                                                if (!empty($profession) && strpos($profession, 'Fastighetsmäklare') !== false) {
                                                    $profession_text .= __('Reg.', TEXTDOMAIN) . ' ';
                                                }
                                                $profession_text .= $profession;
                                                echo esc_html($profession_text);
                                                ?></small>
                                        <?php if (get_field('reg_number', $user_id)) { ?>
                                            <small><a href="tel:<?php echo str_replace(' ', '', get_field('reg_number', $user_id)); ?>"><?php echo get_field('reg_number', $user_id); ?></a></small>
                                        <?php } ?>
                                        <?php if (get_field('email', $user_id)) { ?>
                                            <small><a href="mailto:<?php echo trim(get_field('email', $user_id)); ?>"><?php echo trim(get_field('email', $user_id)); ?></a></small>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div> 
                        </div>
                    </div>
                    <div class="vm-col-12 vm-col-md-7 vm-col-xl-6">
                        <div class="vm-contact__form">
                            <?php if ($timeslots_i == 0) { ?>
                                <style>
                                    .vm-contact__form label[for="select-date"] {
                                        display: none;
                                    }
                                </style>
                            <?php }
                            if (isset($house_details['assignment']['status']['id']) && $house_details['assignment']['status']['id'] == 10) {
                                $shortcode = $fields_option['contact_form_sold'];
                            } else {
                                $shortcode = $fields_option['contact_form'];
                            }
                            if ($shortcode) :
                                echo do_shortcode($shortcode);
                            endif;
                            ?>
                        </div>
                    </div>
                    <?php 
                    endif;
                    ?>
                </div>
            </section>
            <?php
        endif;
    endif;

    // gallery
    if ($gallery_data && is_array($gallery_data)) : ?>
        <div class="vm-grid vm-grid--gallery">
            <div class="vm-col-12 vm-col-md-12">
                <div class="gallery" id="gallery">
                    <div class="vm-grid">
                        <?php
                        $counter = 0;
                        foreach ($gallery_data as $image) :
                            $counter++;
                            if ($counter > 6) break;

                            $extra_class = '';
                            if (
                                isset($image['category'])
                                && (
                                    strpos($image['category'], 'Hemnet Personkort') !== false
                                    || strpos($image['category'], 'Hemnet personkort') !== false
                                    || strpos($image['category'], 'hemnet personkort') !== false
                                )
                            ) {
                                $extra_class = 'contain';
                            }
                        ?>
                            <div class="vm-col-12 vm-col-lg-6 vm-col-xl-4 vm-gallery-item">
                                <img data-src="<?php echo wp_get_attachment_image_url($image['id'], 'large') ?>" alt="House image" class="vm-offerts-list__item-image lazyload <?php echo $extra_class ?>" />
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="gallery-buttons">
                    <?php if ($gallery_data && is_array($gallery_data) && !empty($gallery_data)) : ?>
                        <button id="show-more-btn" data-expanded="false" class="vm-button vm-button--dark"><?php echo __('Alla bilder', TEXTDOMAIN); ?></button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php 
    endif;
    
    // plans
    if ($plans_data && is_array($plans_data)) : ?>
        <div class="vm-grid vm-grid--gallery vm-grid--plans">
            <div class="vm-col-12 vm-col-md-12">
                <div class="gallery" id="gallery">
                    <div class="vm-grid">
                        <?php
                        $counter = 0;
                        foreach ($plans_data as $image) :
                            $counter++;
                            if ($counter > 6) break;
                        ?>
                            <div class="vm-col-12 vm-col-lg-6 vm-col-xl-4 vm-gallery-item">
                                <img data-src="<?php echo wp_get_attachment_image_url($image['id'], 'large') ?>" alt="House image" class="vm-offerts-list__item-image lazyload" />
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="gallery-buttons">
                    <?php if ($plans_data && is_array($plans_data) && !empty($plans_data)) : ?>
                        <button id="show-plans-btn" data-expanded="false" class="vm-button vm-button--dark"><?php echo __('Planritning', TEXTDOMAIN); ?></button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php 
    endif;
    
    // gallery modal
    if ($gallery_data && is_array($gallery_data)) : ?>
        <div class="modal js-gallery-modal">
            <div class="modal-header">
                <div class="vm-header__logo" data-aos="fade-right">
                    <a href="/">
                        <svg width="273" height="22" viewBox="0 0 321 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0.4198H8.62702C12.888 0.4198 15.6473 2.75655 15.6473 6.42065C15.6473 9.17722 14.0745 11.3041 11.2813 11.9338V12.0727C14.6338 12.5975 16.5897 14.9682 16.5897 18.2125C16.5897 22.3303 13.4813 25.0498 8.76607 25.0498H0V0.4198ZM8.20679 10.9213C10.9321 10.9213 12.5388 9.45504 12.5388 6.94542C12.5388 4.43272 10.9321 2.96954 8.20679 2.96954H2.9694V10.9244H8.20679V10.9213ZM8.55595 22.5032C11.6644 22.5032 13.4442 20.827 13.4442 17.9686C13.4442 15.1071 11.5933 13.434 8.51888 13.434H2.96631V22.5063H8.55595V22.5032Z" fill="black" />
                            <path d="M20.2914 0.4198H36.0067V3.00041H23.2608V10.9213H34.2609V13.434H23.2608V22.4692H36.0067V25.0498H20.2914V0.4198Z" fill="black" />
                            <path d="M39.282 0.4198H49.1666C53.4616 0.4198 56.3599 2.89546 56.3599 6.63056C56.3599 9.70198 54.404 11.8288 51.1565 12.4925V12.6314C53.6717 12.7364 55.2414 14.4094 55.2414 16.9561V21.2808C55.2414 23.2008 55.4515 23.9324 56.2889 25.0498H53.1804C52.4821 24.2473 52.272 23.4107 52.272 21.2808V17.5117C52.272 15.07 51.0484 13.9526 48.3942 13.9526H42.2483V25.0468H39.2789V0.4198H39.282ZM48.6414 11.3751C51.5397 11.3751 53.2855 9.80385 53.2855 7.18928C53.2855 4.57471 51.5397 3.0035 48.6414 3.0035H42.2514V11.3751H48.6414Z" fill="black" />
                            <path d="M58.9678 12.8382C58.9678 5.16431 63.5068 0 70.283 0C75.6255 0 79.6423 3.21033 80.6188 8.30365H77.4763C76.812 4.63955 74.0898 2.37379 70.283 2.37379C65.3237 2.37379 62.1102 6.45462 62.1102 12.8413C62.1102 19.157 65.2866 23.1329 70.3881 23.1329C74.8932 23.1329 78.2117 19.9596 78.2117 15.6319V14.7953H73.1813V12.2487H80.9679V25.0529H78.1407V21.04H78.0016C76.3238 23.9015 73.3915 25.4697 69.8288 25.4697C63.3678 25.4697 58.9678 20.3764 58.9678 12.8382Z" fill="black" />
                            <path d="M85.6121 0.4198H101.327V3.00041H88.5815V10.9213H99.5815V13.434H88.5815V22.4692H101.327V25.0498H85.6121V0.4198Z" fill="black" />
                            <path d="M110.872 3.00041H103.503V0.4198H121.208V3.00041H113.838V25.0498H110.869V3.00041H110.872Z" fill="black" />
                            <path d="M122.193 17.166H125.336C125.651 20.9351 128.061 23.0959 131.868 23.0959C135.431 23.0959 137.841 21.2129 137.841 18.3853C137.841 15.9776 136.617 14.9651 132.671 14.0236L130.017 13.397C125.129 12.2456 123.136 10.2916 123.136 6.90839C123.136 2.82756 126.628 0 131.621 0C136.753 0 140.211 2.96647 140.597 7.60602H137.455C137.21 4.32778 135.044 2.37379 131.621 2.37379C128.444 2.37379 126.278 4.11787 126.278 6.66453C126.278 8.89633 127.431 9.87486 131.099 10.7454L133.892 11.409C138.99 12.6314 140.98 14.5144 140.98 18.0736C140.98 22.4692 137.244 25.4697 131.831 25.4697C126.278 25.4697 122.648 22.3303 122.193 17.166Z" fill="black" />
                            <path d="M269.845 0.419861H279.729C284.024 0.419861 286.923 2.89552 286.923 6.63062C286.923 9.70205 284.967 11.8289 281.719 12.4926V12.6315C284.234 12.7364 285.804 14.4095 285.804 16.9562V21.2809C285.804 23.2009 286.014 23.9325 286.852 25.0499H283.743C283.045 24.2473 282.835 23.4108 282.835 21.2809V17.5118C282.835 15.0701 281.611 13.9527 278.957 13.9527H272.811V25.0468H269.842V0.419861H269.845ZM279.204 11.3751C282.102 11.3751 283.848 9.80391 283.848 7.18934C283.848 4.57477 282.102 3.00356 279.204 3.00356H272.814V11.3751H279.204Z" fill="black" />
                            <path d="M289.531 12.7364C289.531 5.09643 294.07 0.00311279 300.88 0.00311279C307.69 0.00311279 312.229 5.09643 312.229 12.7364C312.229 20.3764 307.69 25.4697 300.88 25.4697C294.07 25.4697 289.531 20.3764 289.531 12.7364ZM309.087 12.7364C309.087 6.45774 305.873 2.37382 300.88 2.37382C295.886 2.37382 292.673 6.45465 292.673 12.7364C292.673 19.0151 295.886 23.099 300.88 23.099C305.873 23.099 309.087 19.0151 309.087 12.7364Z" fill="black" />
                            <path d="M314.593 1.09583H313.517V0.719238H316.104V1.09583H315.028V4.31543H314.596V1.09583H314.593Z" fill="black" />
                            <path d="M316.601 0.719238H317.315L318.43 3.97279L319.546 0.719238H320.26V4.31543H319.83V1.11436L318.724 4.31234H318.134L317.028 1.11436V4.31234H316.598V0.719238H316.601Z" fill="black" />
                        </svg>
                    </a>
                </div>
                <span class="close-modal">
                    <?php echo __('Stäng', TEXTDOMAIN); ?>
                    <svg height="21" viewBox="0 0 21 21" width="21" xmlns="http://www.w3.org/2000/svg">
                        <g fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" transform="translate(5 5)">
                            <path d="m10.5 10.5-10-10z"></path>
                            <path d="m10.5.5-10 10"></path>
                        </g>
                    </svg>
                </span>
            </div>
            <div class="img-wrapper">
                <?php
                foreach ($gallery_data as $image) :

                    $extra_class = '';
                    if (
                        isset($image['category'])
                        && (
                            strpos($image['category'], 'Hemnet Personkort') !== false
                            || strpos($image['category'], 'Hemnet personkort') !== false
                            || strpos($image['category'], 'hemnet personkort') !== false
                        )
                    ) {
                        $extra_class = 'contain';
                    }
                ?>
                    <figure class="landscape">
                        <img data-src="<?php echo wp_get_attachment_image_url($image['id'], 'hero-bg') ?>" alt="" rel="gallery" class="ob-img <?php echo $extra_class; ?>">
                    </figure>
                <?php endforeach; ?>
            </div>
        </div>
    <?php 
    endif;
    
    // plans modal
    if ($plans_data && is_array($plans_data)) : ?>
        <div class="modal js-plans-modal">
            <div class="modal-header">
                <div class="vm-header__logo" data-aos="fade-right">
                    <a href="/">
                        <svg width="273" height="22" viewBox="0 0 321 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0.4198H8.62702C12.888 0.4198 15.6473 2.75655 15.6473 6.42065C15.6473 9.17722 14.0745 11.3041 11.2813 11.9338V12.0727C14.6338 12.5975 16.5897 14.9682 16.5897 18.2125C16.5897 22.3303 13.4813 25.0498 8.76607 25.0498H0V0.4198ZM8.20679 10.9213C10.9321 10.9213 12.5388 9.45504 12.5388 6.94542C12.5388 4.43272 10.9321 2.96954 8.20679 2.96954H2.9694V10.9244H8.20679V10.9213ZM8.55595 22.5032C11.6644 22.5032 13.4442 20.827 13.4442 17.9686C13.4442 15.1071 11.5933 13.434 8.51888 13.434H2.96631V22.5063H8.55595V22.5032Z" fill="black" />
                            <path d="M20.2914 0.4198H36.0067V3.00041H23.2608V10.9213H34.2609V13.434H23.2608V22.4692H36.0067V25.0498H20.2914V0.4198Z" fill="black" />
                            <path d="M39.282 0.4198H49.1666C53.4616 0.4198 56.3599 2.89546 56.3599 6.63056C56.3599 9.70198 54.404 11.8288 51.1565 12.4925V12.6314C53.6717 12.7364 55.2414 14.4094 55.2414 16.9561V21.2808C55.2414 23.2008 55.4515 23.9324 56.2889 25.0498H53.1804C52.4821 24.2473 52.272 23.4107 52.272 21.2808V17.5117C52.272 15.07 51.0484 13.9526 48.3942 13.9526H42.2483V25.0468H39.2789V0.4198H39.282ZM48.6414 11.3751C51.5397 11.3751 53.2855 9.80385 53.2855 7.18928C53.2855 4.57471 51.5397 3.0035 48.6414 3.0035H42.2514V11.3751H48.6414Z" fill="black" />
                            <path d="M58.9678 12.8382C58.9678 5.16431 63.5068 0 70.283 0C75.6255 0 79.6423 3.21033 80.6188 8.30365H77.4763C76.812 4.63955 74.0898 2.37379 70.283 2.37379C65.3237 2.37379 62.1102 6.45462 62.1102 12.8413C62.1102 19.157 65.2866 23.1329 70.3881 23.1329C74.8932 23.1329 78.2117 19.9596 78.2117 15.6319V14.7953H73.1813V12.2487H80.9679V25.0529H78.1407V21.04H78.0016C76.3238 23.9015 73.3915 25.4697 69.8288 25.4697C63.3678 25.4697 58.9678 20.3764 58.9678 12.8382Z" fill="black" />
                            <path d="M85.6121 0.4198H101.327V3.00041H88.5815V10.9213H99.5815V13.434H88.5815V22.4692H101.327V25.0498H85.6121V0.4198Z" fill="black" />
                            <path d="M110.872 3.00041H103.503V0.4198H121.208V3.00041H113.838V25.0498H110.869V3.00041H110.872Z" fill="black" />
                            <path d="M122.193 17.166H125.336C125.651 20.9351 128.061 23.0959 131.868 23.0959C135.431 23.0959 137.841 21.2129 137.841 18.3853C137.841 15.9776 136.617 14.9651 132.671 14.0236L130.017 13.397C125.129 12.2456 123.136 10.2916 123.136 6.90839C123.136 2.82756 126.628 0 131.621 0C136.753 0 140.211 2.96647 140.597 7.60602H137.455C137.21 4.32778 135.044 2.37379 131.621 2.37379C128.444 2.37379 126.278 4.11787 126.278 6.66453C126.278 8.89633 127.431 9.87486 131.099 10.7454L133.892 11.409C138.99 12.6314 140.98 14.5144 140.98 18.0736C140.98 22.4692 137.244 25.4697 131.831 25.4697C126.278 25.4697 122.648 22.3303 122.193 17.166Z" fill="black" />
                            <path d="M269.845 0.419861H279.729C284.024 0.419861 286.923 2.89552 286.923 6.63062C286.923 9.70205 284.967 11.8289 281.719 12.4926V12.6315C284.234 12.7364 285.804 14.4095 285.804 16.9562V21.2809C285.804 23.2009 286.014 23.9325 286.852 25.0499H283.743C283.045 24.2473 282.835 23.4108 282.835 21.2809V17.5118C282.835 15.0701 281.611 13.9527 278.957 13.9527H272.811V25.0468H269.842V0.419861H269.845ZM279.204 11.3751C282.102 11.3751 283.848 9.80391 283.848 7.18934C283.848 4.57477 282.102 3.00356 279.204 3.00356H272.814V11.3751H279.204Z" fill="black" />
                            <path d="M289.531 12.7364C289.531 5.09643 294.07 0.00311279 300.88 0.00311279C307.69 0.00311279 312.229 5.09643 312.229 12.7364C312.229 20.3764 307.69 25.4697 300.88 25.4697C294.07 25.4697 289.531 20.3764 289.531 12.7364ZM309.087 12.7364C309.087 6.45774 305.873 2.37382 300.88 2.37382C295.886 2.37382 292.673 6.45465 292.673 12.7364C292.673 19.0151 295.886 23.099 300.88 23.099C305.873 23.099 309.087 19.0151 309.087 12.7364Z" fill="black" />
                            <path d="M314.593 1.09583H313.517V0.719238H316.104V1.09583H315.028V4.31543H314.596V1.09583H314.593Z" fill="black" />
                            <path d="M316.601 0.719238H317.315L318.43 3.97279L319.546 0.719238H320.26V4.31543H319.83V1.11436L318.724 4.31234H318.134L317.028 1.11436V4.31234H316.598V0.719238H316.601Z" fill="black" />
                        </svg>
                    </a>
                </div>
                <span class="close-modal">
                    <?php echo __('Stäng', TEXTDOMAIN); ?>
                    <svg height="21" viewBox="0 0 21 21" width="21" xmlns="http://www.w3.org/2000/svg">
                        <g fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" transform="translate(5 5)">
                            <path d="m10.5 10.5-10-10z"></path>
                            <path d="m10.5.5-10 10"></path>
                        </g>
                    </svg>
                </span>
            </div>
            <div class="img-wrapper">
                <?php foreach ($plans_data as $image) : ?>
                    <figure class="landscape">
                        <img data-src="<?php echo wp_get_attachment_image_url($image['id'], 'hero-bg') ?>" alt="" rel="gallery" class="ob-img">
                    </figure>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

<?php
endwhile;

get_footer();
